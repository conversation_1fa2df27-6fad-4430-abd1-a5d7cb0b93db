# 代码审查检查清单

## 📋 通用检查项

### ✅ 命名规范
- [ ] 类名使用 PascalCase，符合业务含义
- [ ] 方法名使用 camelCase，动词开头
- [ ] 变量名使用 camelCase，名词性
- [ ] 常量使用 UPPER_SNAKE_CASE
- [ ] 布尔变量使用 xxxFlag 命名
- [ ] 包名使用小写字母，符合层次结构

### ✅ 注解使用
- [ ] Controller 类添加 @RestController、@RequestMapping
- [ ] Service 类添加 @Service、@Transactional
- [ ] 工具类添加 @UtilityClass
- [ ] 数据类添加 @Data、@RequiredArgsConstructor
- [ ] 权限接口添加 @SaCheckPermission

### ✅ 异常处理
- [ ] 使用 Assert 工具类进行参数校验
- [ ] 优先使用枚举类型的 Assert 方法
- [ ] 避免直接抛出 RuntimeException
- [ ] 异常信息要明确具体

### ✅ 日志规范
- [ ] 使用 @Slf4j 注解
- [ ] 日志级别使用正确（DEBUG/INFO/WARN/ERROR）
- [ ] 使用参数化日志，避免字符串拼接
- [ ] 敏感信息不记录到日志中

## 🗄️ 数据库操作检查

### ✅ 性能优化
- [ ] 避免 N+1 查询问题
- [ ] 分页查询限制最大条数
- [ ] 合理使用数据库索引

### ✅ 事务管理
- [ ] 正确设置 rollbackFor 属性
- [ ] 避免事务嵌套问题
- [ ] 长事务拆分为多个短事务

## 🌐 API 设计检查

### ✅ RESTful 设计

### ✅ 参数验证
- [ ] 使用 @Valid 进行参数校验
- [ ] Request 类添加验证注解
- [ ] 必填参数使用 @NotNull、@NotBlank
- [ ] 格式校验使用 @Pattern、@Email 等

## 🔧 代码质量检查

### ✅ 代码结构
- [ ] 方法长度不超过 50 行
- [ ] 类长度不超过 500 行
- [ ] 方法参数不超过 5 个
- [ ] 避免深层嵌套（不超过 3 层）

### ✅ 设计原则
- [ ] 单一职责原则
- [ ] 开闭原则
- [ ] 依赖倒置原则
- [ ] 避免重复代码

### ✅ 工具类使用
- [ ] 避免重复造轮子

### ✅ 内存使用
- [ ] 避免内存泄漏
- [ ] 大对象及时释放
- [ ] 集合大小控制
- [ ] 流式处理大数据

### ✅ 并发性能
- [ ] 线程安全考虑
- [ ] 锁使用合理
- [ ] 避免死锁
- [ ] 异步处理适当

### ✅ 代码注释
- [ ] 类和方法有必要的注释
- [ ] 复杂逻辑有解释说明
- [ ] 注释内容准确有效
- [ ] 避免无意义注释