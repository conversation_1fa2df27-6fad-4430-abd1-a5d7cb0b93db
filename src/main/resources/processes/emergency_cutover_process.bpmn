<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  xmlns:camunda="http://camunda.org/schema/1.0/bpmn"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns:modeler="http://camunda.org/schema/modeler/1.0"
                  id="Definitions_emergency_cutover"
                  targetNamespace="http://bpmn.io/schema/bpmn"
                  exporter="Camunda Modeler"
                  exporterVersion="5.37.0"
                  modeler:executionPlatform="Camunda Platform"
                  modeler:executionPlatformVersion="7.20.0">

  <bpmn:process id="emergency_cutover_process" name="紧急割接流程" isExecutable="true">

    <!-- 开始事件 -->
    <bpmn:startEvent id="emergency_start_event" name="紧急开始">
      <bpmn:outgoing>flow_to_emergency_review</bpmn:outgoing>
    </bpmn:startEvent>

    <!-- 紧急审核任务（简化审核） -->
    <bpmn:userTask id="emergency_review_task" name="紧急审核" camunda:assignee="admin">
      <bpmn:documentation>紧急情况下的快速审核，重点检查紧急原因和风险评估</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:taskListener event="complete" delegateExpression="${taskCompleteListener}" />
        <camunda:formData>
          <camunda:formField id="approved" label="审核结果" type="boolean" defaultValue="false" />
          <camunda:formField id="comment" label="审核意见" type="string" />
          <camunda:formField id="emergencyReason" label="紧急原因确认" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>flow_to_emergency_review</bpmn:incoming>
      <bpmn:outgoing>flow_from_emergency_review</bpmn:outgoing>
    </bpmn:userTask>

    <!-- 紧急审核网关 -->
    <bpmn:exclusiveGateway id="emergency_review_gateway" name="紧急审核结果">
      <bpmn:incoming>flow_from_emergency_review</bpmn:incoming>
      <bpmn:outgoing>flow_to_emergency_resource_check</bpmn:outgoing>
      <bpmn:outgoing>flow_to_emergency_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <!-- 紧急资源校验任务 -->
    <bpmn:serviceTask id="emergency_resource_check_task" name="紧急资源校验" camunda:delegateExpression="${resourceCheckDelegate}">
      <bpmn:documentation>紧急情况下的快速资源校验</bpmn:documentation>
      <bpmn:incoming>flow_to_emergency_resource_check</bpmn:incoming>
      <bpmn:outgoing>flow_from_emergency_resource_check</bpmn:outgoing>
    </bpmn:serviceTask>

    <!-- 紧急资源校验网关 -->
    <bpmn:exclusiveGateway id="emergency_resource_gateway" name="紧急资源校验结果">
      <bpmn:incoming>flow_from_emergency_resource_check</bpmn:incoming>
      <bpmn:outgoing>flow_to_emergency_execute</bpmn:outgoing>
      <bpmn:outgoing>flow_to_emergency_resource_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <!-- 紧急执行割接任务 -->
    <bpmn:userTask id="emergency_execute_task" name="紧急执行割接" camunda:assignee="admin">
      <bpmn:documentation>紧急情况下的割接执行，需要特别注意安全和回退</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:taskListener event="complete" delegateExpression="${processTaskListener}" />
        <camunda:formData>
          <camunda:formField id="executionResult" label="执行结果" type="string" />
          <camunda:formField id="actualStartTime" label="实际开始时间" type="date" />
          <camunda:formField id="actualEndTime" label="实际结束时间" type="date" />
          <camunda:formField id="emergencyNotes" label="紧急处理记录" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>flow_to_emergency_execute</bpmn:incoming>
      <bpmn:outgoing>flow_to_emergency_acceptance</bpmn:outgoing>
    </bpmn:userTask>

    <!-- 紧急验收任务 -->
    <bpmn:userTask id="emergency_acceptance_task" name="紧急验收" camunda:assignee="admin">
      <bpmn:documentation>紧急割接后的快速验收</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:taskListener event="complete" delegateExpression="${processTaskListener}" />
        <camunda:formData>
          <camunda:formField id="acceptancePassed" label="验收结果" type="boolean" defaultValue="false" />
          <camunda:formField id="acceptanceComment" label="验收意见" type="string" />
          <camunda:formField id="followUpRequired" label="是否需要后续处理" type="boolean" defaultValue="false" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>flow_to_emergency_acceptance</bpmn:incoming>
      <bpmn:outgoing>flow_from_emergency_acceptance</bpmn:outgoing>
    </bpmn:userTask>

    <!-- 紧急验收网关 -->
    <bpmn:exclusiveGateway id="emergency_acceptance_gateway" name="紧急验收结果">
      <bpmn:incoming>flow_from_emergency_acceptance</bpmn:incoming>
      <bpmn:outgoing>flow_to_emergency_archive</bpmn:outgoing>
      <bpmn:outgoing>flow_to_emergency_re_execute</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <!-- 紧急归档任务 -->
    <bpmn:serviceTask id="emergency_archive_task" name="紧急归档" camunda:delegateExpression="${archiveDelegate}">
      <bpmn:documentation>紧急工单归档，记录紧急处理过程</bpmn:documentation>
      <bpmn:incoming>flow_to_emergency_archive</bpmn:incoming>
      <bpmn:outgoing>flow_to_emergency_end</bpmn:outgoing>
    </bpmn:serviceTask>

    <!-- 结束事件 -->
    <bpmn:endEvent id="emergency_end_event" name="紧急结束">
      <bpmn:incoming>flow_to_emergency_end</bpmn:incoming>
      <bpmn:incoming>flow_to_emergency_rejected</bpmn:incoming>
      <bpmn:incoming>flow_to_emergency_resource_rejected</bpmn:incoming>
    </bpmn:endEvent>

    <!-- 流程连线 -->
    <bpmn:sequenceFlow id="flow_to_emergency_review" sourceRef="emergency_start_event" targetRef="emergency_review_task" />
    <bpmn:sequenceFlow id="flow_from_emergency_review" sourceRef="emergency_review_task" targetRef="emergency_review_gateway" />

    <bpmn:sequenceFlow id="flow_to_emergency_resource_check" sourceRef="emergency_review_gateway" targetRef="emergency_resource_check_task">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="flow_to_emergency_rejected" sourceRef="emergency_review_gateway" targetRef="emergency_end_event">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="flow_from_emergency_resource_check" sourceRef="emergency_resource_check_task" targetRef="emergency_resource_gateway" />

    <bpmn:sequenceFlow id="flow_to_emergency_execute" sourceRef="emergency_resource_gateway" targetRef="emergency_execute_task">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${resourceCheckPassed == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="flow_to_emergency_resource_rejected" sourceRef="emergency_resource_gateway" targetRef="emergency_end_event">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${resourceCheckPassed == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="flow_to_emergency_acceptance" sourceRef="emergency_execute_task" targetRef="emergency_acceptance_task" />
    <bpmn:sequenceFlow id="flow_from_emergency_acceptance" sourceRef="emergency_acceptance_task" targetRef="emergency_acceptance_gateway" />

    <bpmn:sequenceFlow id="flow_to_emergency_archive" sourceRef="emergency_acceptance_gateway" targetRef="emergency_archive_task">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${acceptancePassed == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="flow_to_emergency_re_execute" sourceRef="emergency_acceptance_gateway" targetRef="emergency_execute_task">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${acceptancePassed == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="flow_to_emergency_end" sourceRef="emergency_archive_task" targetRef="emergency_end_event" />

  </bpmn:process>

  <!-- BPMN图形信息 -->
  <bpmndi:BPMNDiagram id="BPMNDiagram_emergency_cutover">
    <bpmndi:BPMNPlane id="BPMNPlane_emergency_cutover" bpmnElement="emergency_cutover_process">

      <!-- 开始事件 -->
      <bpmndi:BPMNShape id="emergency_start_event_di" bpmnElement="emergency_start_event">
        <dc:Bounds x="152" y="209" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="145" y="252" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- 紧急审核任务 -->
      <bpmndi:BPMNShape id="emergency_review_task_di" bpmnElement="emergency_review_task">
        <dc:Bounds x="240" y="187" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- 紧急审核网关 -->
      <bpmndi:BPMNShape id="emergency_review_gateway_di" bpmnElement="emergency_review_gateway" isMarkerVisible="true">
        <dc:Bounds x="395" y="202" width="50" height="50" />
      </bpmndi:BPMNShape>

      <!-- 紧急资源校验任务 -->
      <bpmndi:BPMNShape id="emergency_resource_check_task_di" bpmnElement="emergency_resource_check_task">
        <dc:Bounds x="500" y="187" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- 紧急资源校验网关 -->
      <bpmndi:BPMNShape id="emergency_resource_gateway_di" bpmnElement="emergency_resource_gateway" isMarkerVisible="true">
        <dc:Bounds x="655" y="202" width="50" height="50" />
      </bpmndi:BPMNShape>

      <!-- 紧急执行割接任务 -->
      <bpmndi:BPMNShape id="emergency_execute_task_di" bpmnElement="emergency_execute_task">
        <dc:Bounds x="760" y="187" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- 紧急验收任务 -->
      <bpmndi:BPMNShape id="emergency_acceptance_task_di" bpmnElement="emergency_acceptance_task">
        <dc:Bounds x="920" y="187" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- 紧急验收网关 -->
      <bpmndi:BPMNShape id="emergency_acceptance_gateway_di" bpmnElement="emergency_acceptance_gateway" isMarkerVisible="true">
        <dc:Bounds x="1075" y="202" width="50" height="50" />
      </bpmndi:BPMNShape>

      <!-- 紧急归档任务 -->
      <bpmndi:BPMNShape id="emergency_archive_task_di" bpmnElement="emergency_archive_task">
        <dc:Bounds x="1180" y="187" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- 结束事件 -->
      <bpmndi:BPMNShape id="emergency_end_event_di" bpmnElement="emergency_end_event">
        <dc:Bounds x="1332" y="209" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1325" y="252" width="50" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- 流程连线 -->
      <bpmndi:BPMNEdge id="flow_to_emergency_review_di" bpmnElement="flow_to_emergency_review">
        <di:waypoint x="188" y="227" />
        <di:waypoint x="240" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_from_emergency_review_di" bpmnElement="flow_from_emergency_review">
        <di:waypoint x="340" y="227" />
        <di:waypoint x="395" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_emergency_resource_check_di" bpmnElement="flow_to_emergency_resource_check">
        <di:waypoint x="445" y="227" />
        <di:waypoint x="500" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_emergency_rejected_di" bpmnElement="flow_to_emergency_rejected">
        <di:waypoint x="420" y="202" />
        <di:waypoint x="420" y="120" />
        <di:waypoint x="1350" y="120" />
        <di:waypoint x="1350" y="209" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_from_emergency_resource_check_di" bpmnElement="flow_from_emergency_resource_check">
        <di:waypoint x="600" y="227" />
        <di:waypoint x="655" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_emergency_execute_di" bpmnElement="flow_to_emergency_execute">
        <di:waypoint x="705" y="227" />
        <di:waypoint x="760" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_emergency_resource_rejected_di" bpmnElement="flow_to_emergency_resource_rejected">
        <di:waypoint x="680" y="202" />
        <di:waypoint x="680" y="100" />
        <di:waypoint x="1350" y="100" />
        <di:waypoint x="1350" y="209" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_emergency_acceptance_di" bpmnElement="flow_to_emergency_acceptance">
        <di:waypoint x="860" y="227" />
        <di:waypoint x="920" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_from_emergency_acceptance_di" bpmnElement="flow_from_emergency_acceptance">
        <di:waypoint x="1020" y="227" />
        <di:waypoint x="1075" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_emergency_archive_di" bpmnElement="flow_to_emergency_archive">
        <di:waypoint x="1125" y="227" />
        <di:waypoint x="1180" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_emergency_re_execute_di" bpmnElement="flow_to_emergency_re_execute">
        <di:waypoint x="1100" y="252" />
        <di:waypoint x="1100" y="320" />
        <di:waypoint x="810" y="320" />
        <di:waypoint x="810" y="267" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_emergency_end_di" bpmnElement="flow_to_emergency_end">
        <di:waypoint x="1280" y="227" />
        <di:waypoint x="1332" y="227" />
      </bpmndi:BPMNEdge>

    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>

</bpmn:definitions>