<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL"
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI"
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC"
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI"
                  xmlns:camunda="http://camunda.org/schema/1.0/bpmn"
                  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                  xmlns:modeler="http://camunda.org/schema/modeler/1.0"
                  id="Definitions_planned_cutover"
                  targetNamespace="http://bpmn.io/schema/bpmn"
                  exporter="Camunda Modeler"
                  exporterVersion="5.37.0"
                  modeler:executionPlatform="Camunda Platform"
                  modeler:executionPlatformVersion="7.20.0">

  <bpmn:process id="planned_cutover_process" name="计划割接流程" isExecutable="true">

    <!-- 开始事件 -->
    <bpmn:startEvent id="start_event" name="开始">
      <bpmn:outgoing>flow_to_plan_review</bpmn:outgoing>
    </bpmn:startEvent>

    <!-- 方案审核任务 -->
    <bpmn:userTask id="plan_review_task" name="方案审核" camunda:assignee="admin">
      <bpmn:documentation>地市管理员审核割接方案</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:taskListener event="complete" delegateExpression="${processTaskListener}" />
        <camunda:formData>
          <camunda:formField id="approved" label="审核结果" type="boolean" defaultValue="false" />
          <camunda:formField id="comment" label="审核意见" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>flow_to_plan_review</bpmn:incoming>
      <bpmn:outgoing>flow_from_plan_review</bpmn:outgoing>
    </bpmn:userTask>

    <!-- 方案审核网关 -->
    <bpmn:exclusiveGateway id="plan_review_gateway" name="方案审核结果">
      <bpmn:incoming>flow_from_plan_review</bpmn:incoming>
      <bpmn:outgoing>flow_to_resource_check</bpmn:outgoing>
      <bpmn:outgoing>flow_to_plan_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <!-- 资源校验任务（系统自动执行） -->
    <bpmn:serviceTask id="resource_check_task" name="资源校验" camunda:delegateExpression="${resourceCheckDelegate}">
      <bpmn:documentation>系统自动校验网元资源可用性</bpmn:documentation>
      <bpmn:incoming>flow_to_resource_check</bpmn:incoming>
      <bpmn:outgoing>flow_from_resource_check</bpmn:outgoing>
    </bpmn:serviceTask>

    <!-- 资源校验网关 -->
    <bpmn:exclusiveGateway id="resource_check_gateway" name="资源校验结果">
      <bpmn:incoming>flow_from_resource_check</bpmn:incoming>
      <bpmn:outgoing>flow_to_province_approval</bpmn:outgoing>
      <bpmn:outgoing>flow_to_resource_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <!-- 省公司审批任务 -->
    <bpmn:userTask id="province_approval_task" name="省公司审批" camunda:assignee="admin">
      <bpmn:documentation>省公司管理员审批割接申请</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:taskListener event="complete" delegateExpression="${processTaskListener}" />
        <camunda:formData>
          <camunda:formField id="approved" label="审批结果" type="boolean" defaultValue="false" />
          <camunda:formField id="comment" label="审批意见" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>flow_to_province_approval</bpmn:incoming>
      <bpmn:outgoing>flow_from_province_approval</bpmn:outgoing>
    </bpmn:userTask>

    <!-- 省公司审批网关 -->
    <bpmn:exclusiveGateway id="province_approval_gateway" name="省公司审批结果">
      <bpmn:incoming>flow_from_province_approval</bpmn:incoming>
      <bpmn:outgoing>flow_to_execute_cutover</bpmn:outgoing>
      <bpmn:outgoing>flow_to_province_rejected</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <!-- 执行割接任务 -->
    <bpmn:userTask id="execute_cutover_task" name="执行割接" camunda:assignee="admin">
      <bpmn:documentation>现场人员执行割接操作</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:taskListener event="complete" delegateExpression="${processTaskListener}" />
        <camunda:formData>
          <camunda:formField id="executionResult" label="执行结果" type="string" />
          <camunda:formField id="actualStartTime" label="实际开始时间" type="date" />
          <camunda:formField id="actualEndTime" label="实际结束时间" type="date" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>flow_to_execute_cutover</bpmn:incoming>
      <bpmn:outgoing>flow_to_result_acceptance</bpmn:outgoing>
    </bpmn:userTask>

    <!-- 结果验收任务 -->
    <bpmn:userTask id="result_acceptance_task" name="结果验收" camunda:assignee="admin">
      <bpmn:documentation>质量检查员验收割接结果</bpmn:documentation>
      <bpmn:extensionElements>
        <camunda:taskListener event="complete" delegateExpression="${processTaskListener}" />
        <camunda:formData>
          <camunda:formField id="acceptancePassed" label="验收结果" type="boolean" defaultValue="false" />
          <camunda:formField id="acceptanceComment" label="验收意见" type="string" />
        </camunda:formData>
      </bpmn:extensionElements>
      <bpmn:incoming>flow_to_result_acceptance</bpmn:incoming>
      <bpmn:outgoing>flow_from_result_acceptance</bpmn:outgoing>
    </bpmn:userTask>

    <!-- 验收结果网关 -->
    <bpmn:exclusiveGateway id="acceptance_gateway" name="验收结果">
      <bpmn:incoming>flow_from_result_acceptance</bpmn:incoming>
      <bpmn:outgoing>flow_to_archive</bpmn:outgoing>
      <bpmn:outgoing>flow_to_re_execute</bpmn:outgoing>
    </bpmn:exclusiveGateway>

    <!-- 归档任务 -->
    <bpmn:serviceTask id="archive_task" name="归档" camunda:delegateExpression="${archiveDelegate}">
      <bpmn:documentation>系统自动归档工单</bpmn:documentation>
      <bpmn:incoming>flow_to_archive</bpmn:incoming>
      <bpmn:outgoing>flow_to_end</bpmn:outgoing>
    </bpmn:serviceTask>

    <!-- 结束事件 -->
    <bpmn:endEvent id="end_event" name="结束">
      <bpmn:incoming>flow_to_end</bpmn:incoming>
      <bpmn:incoming>flow_to_plan_rejected</bpmn:incoming>
      <bpmn:incoming>flow_to_resource_rejected</bpmn:incoming>
      <bpmn:incoming>flow_to_province_rejected</bpmn:incoming>
    </bpmn:endEvent>

    <!-- 流程连线 -->
    <bpmn:sequenceFlow id="flow_to_plan_review" sourceRef="start_event" targetRef="plan_review_task" />
    <bpmn:sequenceFlow id="flow_from_plan_review" sourceRef="plan_review_task" targetRef="plan_review_gateway" />

    <bpmn:sequenceFlow id="flow_to_resource_check" sourceRef="plan_review_gateway" targetRef="resource_check_task">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="flow_to_plan_rejected" sourceRef="plan_review_gateway" targetRef="end_event">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="flow_from_resource_check" sourceRef="resource_check_task" targetRef="resource_check_gateway" />

    <bpmn:sequenceFlow id="flow_to_province_approval" sourceRef="resource_check_gateway" targetRef="province_approval_task">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${resourceCheckPassed == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="flow_to_resource_rejected" sourceRef="resource_check_gateway" targetRef="end_event">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${resourceCheckPassed == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="flow_from_province_approval" sourceRef="province_approval_task" targetRef="province_approval_gateway" />

    <bpmn:sequenceFlow id="flow_to_execute_cutover" sourceRef="province_approval_gateway" targetRef="execute_cutover_task">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="flow_to_province_rejected" sourceRef="province_approval_gateway" targetRef="end_event">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="flow_to_result_acceptance" sourceRef="execute_cutover_task" targetRef="result_acceptance_task" />
    <bpmn:sequenceFlow id="flow_from_result_acceptance" sourceRef="result_acceptance_task" targetRef="acceptance_gateway" />

    <bpmn:sequenceFlow id="flow_to_archive" sourceRef="acceptance_gateway" targetRef="archive_task">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${acceptancePassed == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="flow_to_re_execute" sourceRef="acceptance_gateway" targetRef="execute_cutover_task">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${acceptancePassed == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>

    <bpmn:sequenceFlow id="flow_to_end" sourceRef="archive_task" targetRef="end_event" />

  </bpmn:process>

  <!-- BPMN图形信息 -->
  <bpmndi:BPMNDiagram id="BPMNDiagram_planned_cutover">
    <bpmndi:BPMNPlane id="BPMNPlane_planned_cutover" bpmnElement="planned_cutover_process">

      <!-- 开始事件 -->
      <bpmndi:BPMNShape id="start_event_di" bpmnElement="start_event">
        <dc:Bounds x="152" y="209" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="159" y="252" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- 方案审核任务 -->
      <bpmndi:BPMNShape id="plan_review_task_di" bpmnElement="plan_review_task">
        <dc:Bounds x="240" y="187" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- 方案审核网关 -->
      <bpmndi:BPMNShape id="plan_review_gateway_di" bpmnElement="plan_review_gateway" isMarkerVisible="true">
        <dc:Bounds x="395" y="202" width="50" height="50" />
      </bpmndi:BPMNShape>

      <!-- 资源校验任务 -->
      <bpmndi:BPMNShape id="resource_check_task_di" bpmnElement="resource_check_task">
        <dc:Bounds x="500" y="187" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- 资源校验网关 -->
      <bpmndi:BPMNShape id="resource_check_gateway_di" bpmnElement="resource_check_gateway" isMarkerVisible="true">
        <dc:Bounds x="655" y="202" width="50" height="50" />
      </bpmndi:BPMNShape>

      <!-- 省公司审批任务 -->
      <bpmndi:BPMNShape id="province_approval_task_di" bpmnElement="province_approval_task">
        <dc:Bounds x="760" y="187" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- 省公司审批网关 -->
      <bpmndi:BPMNShape id="province_approval_gateway_di" bpmnElement="province_approval_gateway" isMarkerVisible="true">
        <dc:Bounds x="915" y="202" width="50" height="50" />
      </bpmndi:BPMNShape>

      <!-- 执行割接任务 -->
      <bpmndi:BPMNShape id="execute_cutover_task_di" bpmnElement="execute_cutover_task">
        <dc:Bounds x="1020" y="187" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- 结果验收任务 -->
      <bpmndi:BPMNShape id="result_acceptance_task_di" bpmnElement="result_acceptance_task">
        <dc:Bounds x="1180" y="187" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- 验收结果网关 -->
      <bpmndi:BPMNShape id="acceptance_gateway_di" bpmnElement="acceptance_gateway" isMarkerVisible="true">
        <dc:Bounds x="1335" y="202" width="50" height="50" />
      </bpmndi:BPMNShape>

      <!-- 归档任务 -->
      <bpmndi:BPMNShape id="archive_task_di" bpmnElement="archive_task">
        <dc:Bounds x="1440" y="187" width="100" height="80" />
      </bpmndi:BPMNShape>

      <!-- 结束事件 -->
      <bpmndi:BPMNShape id="end_event_di" bpmnElement="end_event">
        <dc:Bounds x="1592" y="209" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1599" y="252" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>

      <!-- 流程连线 -->
      <bpmndi:BPMNEdge id="flow_to_plan_review_di" bpmnElement="flow_to_plan_review">
        <di:waypoint x="188" y="227" />
        <di:waypoint x="240" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_from_plan_review_di" bpmnElement="flow_from_plan_review">
        <di:waypoint x="340" y="227" />
        <di:waypoint x="395" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_resource_check_di" bpmnElement="flow_to_resource_check">
        <di:waypoint x="445" y="227" />
        <di:waypoint x="500" y="227" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="463" y="209" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_plan_rejected_di" bpmnElement="flow_to_plan_rejected">
        <di:waypoint x="420" y="202" />
        <di:waypoint x="420" y="120" />
        <di:waypoint x="1610" y="120" />
        <di:waypoint x="1610" y="209" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="1006" y="102" width="22" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_from_resource_check_di" bpmnElement="flow_from_resource_check">
        <di:waypoint x="600" y="227" />
        <di:waypoint x="655" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_province_approval_di" bpmnElement="flow_to_province_approval">
        <di:waypoint x="705" y="227" />
        <di:waypoint x="760" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_resource_rejected_di" bpmnElement="flow_to_resource_rejected">
        <di:waypoint x="680" y="202" />
        <di:waypoint x="680" y="100" />
        <di:waypoint x="1610" y="100" />
        <di:waypoint x="1610" y="209" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_from_province_approval_di" bpmnElement="flow_from_province_approval">
        <di:waypoint x="860" y="227" />
        <di:waypoint x="915" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_execute_cutover_di" bpmnElement="flow_to_execute_cutover">
        <di:waypoint x="965" y="227" />
        <di:waypoint x="1020" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_province_rejected_di" bpmnElement="flow_to_province_rejected">
        <di:waypoint x="940" y="202" />
        <di:waypoint x="940" y="80" />
        <di:waypoint x="1610" y="80" />
        <di:waypoint x="1610" y="209" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_result_acceptance_di" bpmnElement="flow_to_result_acceptance">
        <di:waypoint x="1120" y="227" />
        <di:waypoint x="1180" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_from_result_acceptance_di" bpmnElement="flow_from_result_acceptance">
        <di:waypoint x="1280" y="227" />
        <di:waypoint x="1335" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_archive_di" bpmnElement="flow_to_archive">
        <di:waypoint x="1385" y="227" />
        <di:waypoint x="1440" y="227" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_re_execute_di" bpmnElement="flow_to_re_execute">
        <di:waypoint x="1360" y="252" />
        <di:waypoint x="1360" y="320" />
        <di:waypoint x="1070" y="320" />
        <di:waypoint x="1070" y="267" />
      </bpmndi:BPMNEdge>

      <bpmndi:BPMNEdge id="flow_to_end_di" bpmnElement="flow_to_end">
        <di:waypoint x="1540" y="227" />
        <di:waypoint x="1592" y="227" />
      </bpmndi:BPMNEdge>

    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>

</bpmn:definitions>