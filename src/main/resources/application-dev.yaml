server:
  port: 10000
  servlet:
    context-path: /

spring:
  application:
    name: cutover-system

  # 数据源配置
  datasource:
    url: ***************************************************************************************************************************************************************************************
    username: root
    password: abc123
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      transaction-isolation: TRANSACTION_READ_COMMITTED

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: abc123
      database: 0
      lettuce:
        pool:
          enabled: true
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: -1


# Camunda配置
camunda:
  bpm:
    # 管理员用户配置
    admin-user:
      id: admin
      password: admin
      firstName: Admin
      lastName: User
      email: <EMAIL>

    # 过滤器配置
    filter:
      create: All tasks

    # 数据库配置
    database:
      schema-update: true
      type: mysql

    # 历史配置
    history-level: full
    generic-properties:
      properties:
        enforce-history-time-to-live: false
#        history-time-to-live: 30

    # 作业执行器配置
    job-execution:
      enabled: false

    # Web应用配置
    webapp:
      index-redirect-enabled: false

    # 自动部署配置
    auto-deployment-enabled: false

# MyBatis-Flex配置
mybatis-flex:
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启缓存
    cache-enabled: true
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

  global-config:
    # 打印SQL
    print-banner: true

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when_authorized

knife4j:
  enable: true
  production: false

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: Authorization
  # token前缀
  token-prefix: Bearer
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 2592000
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒，2小时
  active-timeout: 7200
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false




