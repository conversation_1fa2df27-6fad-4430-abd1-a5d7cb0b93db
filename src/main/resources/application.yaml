server:
  port: 10000
  servlet:
    context-path: /

spring:
  profiles:
    active: dev
  application:
    name: cutover-system

  # 数据源配置
  datasource:
    url: **************************************************************************************************************************************************
    username: root
    password: abc123
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      transaction-isolation: TRANSACTION_READ_COMMITTED

  # Jackson配置
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
    default-property-inclusion: non_null

  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      lettuce:
        pool:
          enabled: true
          max-active: 20
          max-idle: 10
          min-idle: 5
          max-wait: -1


# Camunda配置
camunda:
  bpm:
    # 管理员用户配置
    admin-user:
      id: admin
      password: admin
      firstName: Admin
      lastName: User
      email: <EMAIL>

    # 过滤器配置
    filter:
      create: All tasks

    # 数据库配置
    database:
      schema-update: true
      type: mysql

    # 历史配置
    history-level: full
    generic-properties:
      properties:
        enforce-history-time-to-live: false
    #        history-time-to-live: 30

    # 作业执行器配置
    job-execution:
      enabled: true

    # Web应用配置
    webapp:
      index-redirect-enabled: false

    # 自动部署配置
    auto-deployment-enabled: false

# MyBatis-Flex配置
mybatis-flex:
  configuration:
    # 开启驼峰命名转换
    map-underscore-to-camel-case: true
    # 开启缓存
    cache-enabled: true
    # 日志实现
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

  global-config:
    # 打印SQL
    print-banner: true

# 管理端点配置
management:
  endpoints:
    web:
      exposure:
        # 暴露必要的端点
        include: health,info,metrics,loggers,env
      base-path: /actuator
      cors:
        allowed-origins: "*"
        allowed-methods: GET,POST
  endpoint:
    health:
      # 健康检查详情显示策略
      show-details: always
      show-components: always
    metrics:
      enabled: true
    info:
      enabled: true
    loggers:
      enabled: true
    env:
      enabled: true

  # 健康检查配置
  health:
    # 数据库健康检查
    db:
      enabled: true
    # Redis健康检查
    redis:
      enabled: true
    # 磁盘空间检查
    diskspace:
      enabled: true
      threshold: 10MB
    # 自定义健康检查
    defaults:
      enabled: true

  # 指标配置
  metrics:
    # JVM指标
    enable:
      jvm: true
      system: true
      web: true
      process: true
    # 分布配置
    distribution:
      percentiles-histogram:
        http.server.requests: true
      percentiles:
        http.server.requests: 0.5,0.9,0.95,0.99
      slo:
        http.server.requests: 10ms,50ms,100ms,200ms,500ms,1s,2s,5s

  # 应用信息配置
  info:
    env:
      enabled: true
    java:
      enabled: true
    os:
      enabled: true
    git:
      mode: full
    build:
      enabled: true

knife4j:
  enable: true
  production: false

# Sa-Token配置
sa-token:
  # token名称 (同时也是cookie名称)
  token-name: satoken
  # token有效期，单位s 默认30天, -1代表永不过期
  timeout: 2592000
  # token临时有效期 (指定时间内无操作就视为token过期) 单位: 秒，2小时
  active-timeout: 7200
  # 是否允许同一账号并发登录 (为true时允许一起登录, 为false时新登录挤掉旧登录)
  is-concurrent: true
  # 在多人登录同一账号时，是否共用一个token (为true时所有登录共用一个token, 为false时每次登录新建一个token)
  is-share: true
  # token风格
  token-style: uuid
  # 是否输出操作日志
  is-log: false



