-- ================================
-- 流程模块初始化脚本
-- 包含：流程定义表结构创建 + 示例数据
-- 作者：arnan
-- 创建时间：2025-01-26
-- ================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ================================
-- 1. 清空现有数据
-- ================================

DROP TABLE IF EXISTS `proc_definition`;

-- ================================
-- 2. 创建表结构
-- ================================

-- 流程定义表
CREATE TABLE `proc_definition` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  
  -- 流程基本信息
  `process_key` varchar(100) NOT NULL COMMENT '流程定义Key（唯一标识）',
  `process_name` varchar(200) NOT NULL COMMENT '流程名称',
  `description` varchar(500) DEFAULT NULL COMMENT '流程描述',
  `category` varchar(100) DEFAULT NULL COMMENT '流程分类',
  `version` int NOT NULL DEFAULT 1 COMMENT '流程版本',
  
  -- BPMN相关内容
  `bpmn_xml` longtext NOT NULL COMMENT 'BPMN XML内容',
  `diagram_svg` longtext DEFAULT NULL COMMENT '流程图SVG内容',
  
  -- 部署相关信息
  `is_deployed` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否已部署（0-未部署，1-已部署）',
  `deployment_id` varchar(64) DEFAULT NULL COMMENT 'Camunda部署ID',
  `camunda_process_definition_id` varchar(64) DEFAULT NULL COMMENT 'Camunda流程定义ID',
  `status` varchar(20) NOT NULL DEFAULT 'DRAFT' COMMENT '流程定义状态（DRAFT-草稿，ACTIVE-激活，SUSPENDED-暂停，DISABLED-停用）',
  
  -- 设计器相关信息
  `designer_type` varchar(50) DEFAULT 'BPMN_JS' COMMENT '设计器类型（BPMN_JS, CAMUNDA_MODELER等）',
  `designer_config` json DEFAULT NULL COMMENT '设计器配置JSON',
  
  -- 扩展信息
  `tags` json DEFAULT NULL COMMENT '流程标签（JSON数组）',
  `remarks` varchar(1000) DEFAULT NULL COMMENT '备注',
  
  -- 基础字段（继承自BaseEntity）
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` bigint DEFAULT NULL COMMENT '创建人ID',
  `update_by` bigint DEFAULT NULL COMMENT '更新人ID',

  PRIMARY KEY (`id`),
  
  -- 唯一索引
  UNIQUE KEY `uk_process_key_version` (`process_key`, `version`) COMMENT '流程Key和版本唯一索引',
  
  -- 普通索引
  KEY `idx_process_name` (`process_name`) COMMENT '流程名称索引',
  KEY `idx_category` (`category`) COMMENT '流程分类索引',
  KEY `idx_status` (`status`) COMMENT '流程状态索引',
  KEY `idx_is_deployed` (`is_deployed`) COMMENT '部署状态索引',
  KEY `idx_deployment_id` (`deployment_id`) COMMENT 'Camunda部署ID索引',
  KEY `idx_create_time` (`create_time`) COMMENT '创建时间索引',
  KEY `idx_create_by` (`create_by`) COMMENT '创建人索引',

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='流程定义表';

-- ================================
-- 3. 插入示例数据
-- ================================

-- 插入演示流程定义
INSERT INTO `proc_definition` (
  `process_key`, 
  `process_name`, 
  `description`, 
  `category`, 
  `version`, 
  `bpmn_xml`, 
  `status`, 
  `designer_type`,
  `designer_config`,
  `tags`,
  `create_by`
) VALUES (
  'demo_approval_process',
  '演示审批流程',
  '这是一个演示用的简单审批流程，包含申请、审批、结束三个环节',
  '审批流程',
  1,
  '<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" 
                  xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" 
                  xmlns:di="http://www.omg.org/spec/DD/20100524/DI" 
                  id="Definitions_1" 
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="demo_approval_process" name="演示审批流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_1" name="开始">
      <bpmn:outgoing>Flow_1</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_1" name="提交申请">
      <bpmn:incoming>Flow_1</bpmn:incoming>
      <bpmn:outgoing>Flow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_2" name="审批">
      <bpmn:incoming>Flow_2</bpmn:incoming>
      <bpmn:outgoing>Flow_3</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_1" name="结束">
      <bpmn:incoming>Flow_3</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_1" sourceRef="StartEvent_1" targetRef="Task_1" />
    <bpmn:sequenceFlow id="Flow_2" sourceRef="Task_1" targetRef="Task_2" />
    <bpmn:sequenceFlow id="Flow_3" sourceRef="Task_2" targetRef="EndEvent_1" />
  </bpmn:process>
</bpmn:definitions>',
  'DRAFT',
  'BPMN_JS',
  '{"theme": "default", "language": "zh-CN", "autoSave": true}',
  '["审批", "演示", "简单流程"]',
  1
);

-- 插入请假流程定义
INSERT INTO `proc_definition` (
  `process_key`, 
  `process_name`, 
  `description`, 
  `category`, 
  `version`, 
  `bpmn_xml`, 
  `status`, 
  `designer_type`,
  `tags`,
  `create_by`
) VALUES (
  'leave_request_process',
  '请假申请流程',
  '员工请假申请审批流程，包含申请、部门审批、HR审批等环节',
  '人事流程',
  1,
  '<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" 
                  id="Definitions_2" 
                  targetNamespace="http://bpmn.io/schema/bpmn">
  <bpmn:process id="leave_request_process" name="请假申请流程" isExecutable="true">
    <bpmn:startEvent id="StartEvent_2" name="提交请假申请">
      <bpmn:outgoing>Flow_4</bpmn:outgoing>
    </bpmn:startEvent>
    <bpmn:userTask id="Task_3" name="部门经理审批">
      <bpmn:incoming>Flow_4</bpmn:incoming>
      <bpmn:outgoing>Flow_5</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:userTask id="Task_4" name="HR审批">
      <bpmn:incoming>Flow_5</bpmn:incoming>
      <bpmn:outgoing>Flow_6</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:endEvent id="EndEvent_2" name="审批完成">
      <bpmn:incoming>Flow_6</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:sequenceFlow id="Flow_4" sourceRef="StartEvent_2" targetRef="Task_3" />
    <bpmn:sequenceFlow id="Flow_5" sourceRef="Task_3" targetRef="Task_4" />
    <bpmn:sequenceFlow id="Flow_6" sourceRef="Task_4" targetRef="EndEvent_2" />
  </bpmn:process>
</bpmn:definitions>',
  'DRAFT',
  'BPMN_JS',
  '["请假", "人事", "审批"]',
  1
);

-- ================================
-- 4. 显示统计信息
-- ================================

SELECT '流程模块初始化完成' as status,
       (SELECT COUNT(*) FROM proc_definition) as total_process_definitions;

-- 显示示例流程信息
SELECT '=== 示例流程信息 ===' as info,
       'process_key: demo_approval_process, name: 演示审批流程' as process1,
       'process_key: leave_request_process, name: 请假申请流程' as process2;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;
