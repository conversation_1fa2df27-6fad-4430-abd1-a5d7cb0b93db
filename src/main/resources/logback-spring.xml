<?xml version="1.0" encoding="UTF-8"?>
<configuration scan="true" scanPeriod="30 seconds" debug="false">
    <!-- 引入Spring Boot提供的logback扩展，解决%clr和%wEx等转换符问题 -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml"/>
    <!-- 1. 定义常量 -->
    <property name="LOG_BASE_PATH" value="logs"/>  <!-- 日志根目录 -->
    <property name="APP_NAME" value="cutover-control"/>  <!-- 应用名称 -->
    <property name="FILE_MAX_SIZE" value="10MB"/>  <!-- 单个日志文件最大大小 -->
    <property name="MAX_HISTORY" value="30"/>  <!-- 日志保留天数 -->

    <!-- 2. 控制台输出配置 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <!-- 仅在开发环境生效 -->
        <springProfile name="dev">
            <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                <!-- 带颜色的日志格式 -->
                <pattern>%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(%5p) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n%ex</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </springProfile>

        <!-- 生产环境控制台输出简化格式（可选） -->
        <springProfile name="prod">
            <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
                <charset>UTF-8</charset>
            </encoder>
        </springProfile>
    </appender>

    <!-- 3. 普通日志文件输出（INFO及以上级别） -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <!-- 日志文件路径 -->
        <file>${LOG_BASE_PATH}/${APP_NAME}.log</file>

        <!-- 滚动策略：按时间和大小分割 -->
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 滚动文件名格式（按天分割，超过大小生成新文件） -->
            <fileNamePattern>${LOG_BASE_PATH}/${APP_NAME}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>${MAX_HISTORY}</maxHistory>  <!-- 保留30天日志 -->

            <!-- 配合大小分割策略 -->
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${FILE_MAX_SIZE}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>

        <!-- 日志格式（无颜色，便于存储） -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>

        <!-- 只输出INFO及以上级别日志 -->
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>INFO</level>
        </filter>
    </appender>

    <!-- 4. 错误日志单独输出（ERROR级别） -->
    <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_BASE_PATH}/error/${APP_NAME}_error.log</file>

        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_BASE_PATH}/error/${APP_NAME}_error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>${MAX_HISTORY}</maxHistory>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${FILE_MAX_SIZE}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
        </rollingPolicy>

        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
            <charset>UTF-8</charset>
        </encoder>

        <!-- 只输出ERROR级别日志 -->
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 5. 异步输出日志（提高性能） -->
    <appender name="ASYNC_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>  <!-- 不丢弃任何日志 -->
        <queueSize>1024</queueSize>  <!-- 缓冲区大小 -->
        <appender-ref ref="FILE"/>  <!-- 引用上面定义的FILE appender -->
    </appender>

    <appender name="ASYNC_ERROR_FILE" class="ch.qos.logback.classic.AsyncAppender">
        <discardingThreshold>0</discardingThreshold>
        <queueSize>1024</queueSize>
        <appender-ref ref="ERROR_FILE"/>
    </appender>

    <!-- 6. 多环境日志级别配置 -->
    <!-- 开发环境：输出DEBUG及以上级别到控制台和文件 -->
    <springProfile name="dev">
        <root level="DEBUG">
            <appender-ref ref="CONSOLE"/>
<!--            <appender-ref ref="FILE"/>-->
<!--            <appender-ref ref="ERROR_FILE"/>-->
        </root>
        <logger name="org.camunda" level="WARN"/>
    </springProfile>

    <!-- 生产环境：输出INFO及以上级别，使用异步输出提高性能 -->
    <springProfile name="prod">
        <root level="INFO">
            <appender-ref ref="ASYNC_FILE"/>
            <appender-ref ref="ASYNC_ERROR_FILE"/>
            <!-- 生产环境可关闭控制台输出 -->
            <!-- <appender-ref ref="CONSOLE"/> -->
        </root>

        <!-- 第三方框架日志级别控制（如Spring框架只输出WARN及以上） -->
        <logger name="org.springframework" level="WARN"/>
        <logger name="org.camunda" level="WARN"/>
    </springProfile>

    <!-- 测试环境：输出INFO及以上级别到文件 -->
    <springProfile name="test">
        <root level="INFO">
            <appender-ref ref="FILE"/>
            <appender-ref ref="ERROR_FILE"/>
        </root>
    </springProfile>
</configuration>
