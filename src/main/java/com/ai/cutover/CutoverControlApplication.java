package com.ai.cutover;

import org.camunda.bpm.spring.boot.starter.annotation.EnableProcessApplication;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * 中国联通陕西分公司网络割接可视化系统
 *
 * 系统功能： 1. 工单管理 - 割接工单的创建、编辑、提交、查询 2. 流程管理 - 基于Camunda的割接审批流程 3. 资源管理 - 网元资源的管理和状态同步
 *
 * 技术栈： - Spring Boot 3.4.7 - Camunda BPM 7.20.0 - MyBatis-Flex 1.11.1 - MySQL 8.0
 *
 * <AUTHOR>
 */
@SpringBootApplication
@EnableProcessApplication
@EnableTransactionManagement
public class CutoverControlApplication {

	public static void main(String[] args) {
		SpringApplication.run(CutoverControlApplication.class, args);
	}

}
