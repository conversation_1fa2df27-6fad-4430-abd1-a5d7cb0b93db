package com.ai.cutover.common.exception;

import cn.dev33.satoken.exception.NotLoginException;
import com.ai.cutover.common.constant.ErrorMessages;
import com.ai.cutover.common.result.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.util.stream.Collectors;

/**
 * 全局异常处理器 统一处理系统中的各种异常，返回标准的错误响应
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

	/**
	 * 处理业务异常
	 */
	@ExceptionHandler(BusinessException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	public Result<Void> handleBusinessException(BusinessException e) {
		log.warn("业务异常: {}", e.getMessage());
		return Result.error(e.getCode(), e.getMessage());
	}

	/**
	 * 处理参数校验异常（@RequestBody）
	 */
	@ExceptionHandler(MethodArgumentNotValidException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	public Result<Void> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {
		String message = e.getBindingResult()
			.getFieldErrors()
			.stream()
			.map(FieldError::getDefaultMessage)
			.collect(Collectors.joining(", "));
		log.warn("参数校验异常: {}", message);
		return Result
			.badRequest(new BusinessException(ErrorMessages.System.PARAM_VALIDATION_FAILED, message).getMessage());
	}

	/**
	 * 处理参数校验异常（@ModelAttribute）
	 */
	@ExceptionHandler(BindException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	public Result<Void> handleBindException(BindException e) {
		String message = e.getBindingResult()
			.getFieldErrors()
			.stream()
			.map(FieldError::getDefaultMessage)
			.collect(Collectors.joining(", "));
		log.warn("参数绑定异常: {}", message);
		return Result
			.badRequest(new BusinessException(ErrorMessages.System.PARAM_VALIDATION_FAILED, message).getMessage());
	}

	/**
	 * 处理约束校验异常（@Validated）
	 */
	@ExceptionHandler(ConstraintViolationException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	public Result<Void> handleConstraintViolationException(ConstraintViolationException e) {
		String message = e.getConstraintViolations()
			.stream()
			.map(ConstraintViolation::getMessage)
			.collect(Collectors.joining(", "));
		log.warn("约束校验异常: {}", message);
		return Result
			.badRequest(new BusinessException(ErrorMessages.System.PARAM_VALIDATION_FAILED, message).getMessage());
	}

	/**
	 * 处理非法参数异常
	 */
	@ExceptionHandler(IllegalArgumentException.class)
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	public Result<Void> handleIllegalArgumentException(IllegalArgumentException e) {
		log.warn("非法参数异常: {}", e.getMessage());
		return Result.badRequest(new BusinessException(ErrorMessages.System.PARAM_ERROR, e.getMessage()).getMessage());
	}

	/**
	 * 处理未登录异常
	 */
	@ExceptionHandler(NotLoginException.class)
	@ResponseStatus(HttpStatus.UNAUTHORIZED)
	public Result<Void> handleNotLoginException(NotLoginException e) {
		log.warn("用户未登录异常: {}", e.getMessage());
		return Result.unauthorized(ErrorMessages.System.USER_NOT_LOGIN);
	}

	/**
	 * 处理空指针异常
	 */
	@ExceptionHandler(NullPointerException.class)
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	public Result<Void> handleNullPointerException(NullPointerException e) {
		log.error("空指针异常", e);
		return Result.error(ErrorMessages.System.SYSTEM_INTERNAL_ERROR);
	}

	/**
	 * 处理运行时异常
	 */
	@ExceptionHandler(RuntimeException.class)
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	public Result<Void> handleRuntimeException(RuntimeException e) {
		log.error("运行时异常", e);
		return Result
			.error(new BusinessException(ErrorMessages.System.SYSTEM_INTERNAL_ERROR_WITH_MESSAGE, e.getMessage())
				.getMessage());
	}

	/**
	 * 处理数据库异常
	 */
	@ExceptionHandler({ org.springframework.dao.DataIntegrityViolationException.class,
			org.springframework.dao.DuplicateKeyException.class })
	@ResponseStatus(HttpStatus.BAD_REQUEST)
	public Result<Void> handleDataIntegrityViolationException(Exception e) {
		log.warn("数据库约束异常: {}", e.getMessage());
		String message = ErrorMessages.System.DATA_OPERATION_FAILED;
		if (e.getMessage().contains("Duplicate entry")) {
			message = ErrorMessages.System.DATA_ALREADY_EXISTS_CHECK;
		}
		return Result.badRequest(message);
	}

	/**
	 * 处理数据访问异常
	 */
	@ExceptionHandler(org.springframework.dao.DataAccessException.class)
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	public Result<Void> handleDataAccessException(org.springframework.dao.DataAccessException e) {
		log.error("数据访问异常", e);
		return Result.error(ErrorMessages.System.DATA_ACCESS_FAILED);
	}

	/**
	 * 处理其他异常
	 */
	@ExceptionHandler(Exception.class)
	@ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
	public Result<Void> handleException(Exception e) {
		log.error("系统异常", e);
		return Result.error(ErrorMessages.System.SYSTEM_INTERNAL_ERROR);
	}

}
