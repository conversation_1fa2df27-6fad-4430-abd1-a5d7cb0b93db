package com.ai.cutover.common.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 业务异常类 用于处理业务逻辑中的异常情况
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class BusinessException extends RuntimeException {

	/**
	 * 错误码
	 */
	private Integer code;

	/**
	 * 错误消息
	 */
	private String message;

	public BusinessException(String message) {
		super(message);
		this.code = 500;
		this.message = message;
	}

	public BusinessException(Integer code, String message) {
		super(message);
		this.code = code;
		this.message = message;
	}

	public BusinessException(String message, Throwable cause) {
		super(message, cause);
		this.code = 500;
		this.message = message;
	}

	public BusinessException(Integer code, String message, Throwable cause) {
		super(message, cause);
		this.code = code;
		this.message = message;
	}

	/**
	 * 支持占位符的构造方法
	 */
	public BusinessException(String messageTemplate, Object... params) {
		this(500, formatMessage(messageTemplate, params));
	}

	/**
	 * 支持占位符的构造方法（带错误码）
	 */
	public BusinessException(Integer code, String messageTemplate, Object... params) {
		this(code, formatMessage(messageTemplate, params));
	}

	/**
	 * 支持占位符的构造方法（带异常原因）
	 */
	public BusinessException(String messageTemplate, Throwable cause, Object... params) {
		this(500, formatMessage(messageTemplate, params), cause);
	}

	/**
	 * 支持占位符的构造方法（带错误码和异常原因）
	 */
	public BusinessException(Integer code, String messageTemplate, Throwable cause, Object... params) {
		this(code, formatMessage(messageTemplate, params), cause);
	}

	/**
	 * 格式化错误消息，支持{}占位符
	 * @param template 消息模板
	 * @param params 参数
	 * @return 格式化后的消息
	 */
	private static String formatMessage(String template, Object... params) {
		if (template == null) {
			return null;
		}
		if (params == null || params.length == 0) {
			return template;
		}
		String result = template;
		for (Object param : params) {
			result = result.replaceFirst("\\{}", String.valueOf(param));
		}
		return result;
	}

}
