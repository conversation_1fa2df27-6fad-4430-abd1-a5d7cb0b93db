package com.ai.cutover.common.service;

import com.ai.cutover.common.constant.ErrorMessages;
import com.ai.cutover.common.exception.BusinessException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Collection;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Redis缓存服务类 提供常用的Redis操作方法
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RedisService {

	private final RedisTemplate<String, Object> redisTemplate;

	/**
	 * 设置缓存
	 * @param key 键
	 * @param value 值
	 */
	public void set(String key, Object value) {
		executeWithExceptionHandling(() -> redisTemplate.opsForValue().set(key, value),
				ErrorMessages.Cache.CACHE_SET_FAILED, key, value);
	}

	/**
	 * 设置缓存并指定过期时间
	 * @param key 键
	 * @param value 值
	 * @param timeout 过期时间
	 * @param unit 时间单位
	 */
	public void set(String key, Object value, long timeout, TimeUnit unit) {
		executeWithExceptionHandling(() -> redisTemplate.opsForValue().set(key, value, timeout, unit),
				ErrorMessages.Cache.CACHE_SET_WITH_TIMEOUT_FAILED, key, value, timeout, unit);
	}

	/**
	 * 设置缓存并指定过期时间
	 * @param key 键
	 * @param value 值
	 * @param duration 过期时间
	 */
	public void set(String key, Object value, Duration duration) {
		executeWithExceptionHandling(() -> redisTemplate.opsForValue().set(key, value, duration),
				ErrorMessages.Cache.CACHE_SET_WITH_DURATION_FAILED, key, value, duration);
	}

	/**
	 * 获取缓存
	 * @param key 键
	 * @return 值
	 */
	public Object get(String key) {
		try {
			return redisTemplate.opsForValue().get(key);
		}
		catch (Exception e) {
			log.error(ErrorMessages.Cache.CACHE_GET_FAILED, key, e);
			throw new BusinessException(ErrorMessages.Cache.CACHE_GET_FAILED, e, key);
		}
	}

	/**
	 * 获取缓存并转换为指定类型
	 * @param key 键
	 * @param clazz 目标类型
	 * @param <T> 泛型
	 * @return 值
	 */
	public <T> T get(String key, Class<T> clazz) {
		try {
			Object value = redisTemplate.opsForValue().get(key);
			return convertValue(value, clazz, key);
		}
		catch (Exception e) {
			log.error(ErrorMessages.Cache.CACHE_GET_FAILED, key, e);
			throw new BusinessException(ErrorMessages.Cache.CACHE_GET_FAILED, e, key);
		}
	}

	/**
	 * 获取缓存并转换为指定类型，提供默认值
	 * @param key 键
	 * @param clazz 目标类型
	 * @param defaultValue 默认值
	 * @param <T> 泛型
	 * @return 值
	 */
	public <T> T get(String key, Class<T> clazz, T defaultValue) {
		T result = get(key, clazz);
		return result != null ? result : defaultValue;
	}

	/**
	 * 获取Long类型值，提供默认值
	 * @param key 键
	 * @param defaultValue 默认值
	 * @return Long值
	 */
	public Long getLong(String key, Long defaultValue) {
		return get(key, Long.class, defaultValue);
	}

	/**
	 * 获取Long类型值，默认值为0
	 * @param key 键
	 * @return Long值
	 */
	public Long getLong(String key) {
		return getLong(key, 0L);
	}

	/**
	 * 获取Integer类型值，提供默认值
	 * @param key 键
	 * @param defaultValue 默认值
	 * @return Integer值
	 */
	public Integer getInteger(String key, Integer defaultValue) {
		return get(key, Integer.class, defaultValue);
	}

	/**
	 * 获取Integer类型值，默认值为0
	 * @param key 键
	 * @return Integer值
	 */
	public Integer getInteger(String key) {
		return getInteger(key, 0);
	}

	/**
	 * 获取String类型值，提供默认值
	 * @param key 键
	 * @param defaultValue 默认值
	 * @return String值
	 */
	public String getString(String key, String defaultValue) {
		return get(key, String.class, defaultValue);
	}

	/**
	 * 获取String类型值，默认值为null
	 * @param key 键
	 * @return String值
	 */
	public String getString(String key) {
		return getString(key, null);
	}

	/**
	 * 获取Boolean类型值，提供默认值
	 * @param key 键
	 * @param defaultValue 默认值
	 * @return Boolean值
	 */
	public Boolean getBoolean(String key, Boolean defaultValue) {
		return get(key, Boolean.class, defaultValue);
	}

	/**
	 * 获取Boolean类型值，默认值为false
	 * @param key 键
	 * @return Boolean值
	 */
	public Boolean getBoolean(String key) {
		return getBoolean(key, false);
	}

	/**
	 * 删除缓存
	 * @param key 键
	 * @return 是否删除成功
	 */
	public Boolean delete(String key) {
		try {
			return redisTemplate.delete(key);
		}
		catch (Exception e) {
			log.error(ErrorMessages.Cache.CACHE_DELETE_FAILED, key, e);
			throw new BusinessException(ErrorMessages.Cache.CACHE_DELETE_FAILED, e, key);
		}
	}

	/**
	 * 批量删除缓存
	 * @param keys 键集合
	 * @return 删除的数量
	 */
	public Long delete(Collection<String> keys) {
		try {
			return redisTemplate.delete(keys);
		}
		catch (Exception e) {
			log.error(ErrorMessages.Cache.CACHE_DELETE_BATCH_FAILED, keys, e);
			throw new BusinessException(ErrorMessages.Cache.CACHE_DELETE_BATCH_FAILED, e, keys);
		}
	}

	/**
	 * 判断键是否存在
	 * @param key 键
	 * @return 是否存在
	 */
	public Boolean hasKey(String key) {
		try {
			return redisTemplate.hasKey(key);
		}
		catch (Exception e) {
			log.error(ErrorMessages.Cache.CACHE_HAS_KEY_FAILED, key, e);
			throw new BusinessException(ErrorMessages.Cache.CACHE_HAS_KEY_FAILED, e, key);
		}
	}

	/**
	 * 设置键的过期时间
	 * @param key 键
	 * @param timeout 过期时间
	 * @param unit 时间单位
	 * @return 是否设置成功
	 */
	public Boolean expire(String key, long timeout, TimeUnit unit) {
		try {
			return redisTemplate.expire(key, timeout, unit);
		}
		catch (Exception e) {
			log.error(ErrorMessages.Cache.CACHE_EXPIRE_FAILED, key, timeout, unit, e);
			throw new BusinessException(ErrorMessages.Cache.CACHE_EXPIRE_FAILED, e, key, timeout, unit);
		}
	}

	/**
	 * 获取键的过期时间
	 * @param key 键
	 * @return 过期时间（秒）
	 */
	public Long getExpire(String key) {
		try {
			return redisTemplate.getExpire(key);
		}
		catch (Exception e) {
			log.error(ErrorMessages.Cache.CACHE_GET_EXPIRE_FAILED, key, e);
			throw new BusinessException(ErrorMessages.Cache.CACHE_GET_EXPIRE_FAILED, e, key);
		}
	}

	/**
	 * 递增
	 * @param key 键
	 * @param delta 递增值
	 * @return 递增后的值
	 */
	public Long increment(String key, long delta) {
		try {
			return redisTemplate.opsForValue().increment(key, delta);
		}
		catch (Exception e) {
			log.error(ErrorMessages.Cache.CACHE_INCREMENT_FAILED, key, delta, e);
			throw new BusinessException(ErrorMessages.Cache.CACHE_INCREMENT_FAILED, e, key, delta);
		}
	}

	/**
	 * 递增
	 * @param key 键
	 * @return 递增后的值
	 */
	public Long increment(String key) {
		return increment(key, 1);
	}

	/**
	 * 递减
	 * @param key 键
	 * @param delta 递减值
	 * @return 递减后的值
	 */
	public Long decrement(String key, long delta) {
		try {
			return redisTemplate.opsForValue().decrement(key, delta);
		}
		catch (Exception e) {
			log.error(ErrorMessages.Cache.CACHE_DECREMENT_FAILED, key, delta, e);
			throw new BusinessException(ErrorMessages.Cache.CACHE_DECREMENT_FAILED, e, key, delta);
		}
	}

	/**
	 * 递减
	 * @param key 键
	 * @return 递减后的值
	 */
	public Long decrement(String key) {
		return decrement(key, 1);
	}

	/**
	 * 获取匹配的键
	 * @param pattern 模式
	 * @return 键集合
	 */
	public Set<String> keys(String pattern) {
		try {
			return redisTemplate.keys(pattern);
		}
		catch (Exception e) {
			log.error(ErrorMessages.Cache.CACHE_KEYS_FAILED, pattern, e);
			throw new BusinessException(ErrorMessages.Cache.CACHE_KEYS_FAILED, e, pattern);
		}
	}

	/**
	 * 智能类型转换方法
	 * @param value 原始值
	 * @param clazz 目标类型
	 * @param key 键（用于日志）
	 * @param <T> 泛型
	 * @return 转换后的值
	 */
	@SuppressWarnings("unchecked")
	private <T> T convertValue(Object value, Class<T> clazz, String key) {
		if (value == null) {
			return null;
		}

		// 直接类型匹配
		if (clazz.isInstance(value)) {
			return (T) value;
		}

		try {
			// 数值类型转换
			if (value instanceof Number numberValue && Number.class.isAssignableFrom(clazz)) {
				return convertNumber(numberValue, clazz);
			}

			// 字符串类型转换
			if (clazz == String.class) {
				return (T) value.toString();
			}

			// 布尔类型转换（包括Boolean和boolean）
			if (clazz == Boolean.class || clazz == boolean.class) {
				return convertToBoolean(value);
			}

			// 字符串到数值类型的转换
			if (value instanceof String stringValue && Number.class.isAssignableFrom(clazz)) {
				return convertStringToNumber(stringValue, clazz);
			}

			// 如果无法转换，抛出异常
			log.error(ErrorMessages.Cache.CACHE_TYPE_NOT_SUPPORTED, key, value.getClass().getSimpleName(),
					clazz.getSimpleName());
			throw new BusinessException(ErrorMessages.Cache.CACHE_TYPE_NOT_SUPPORTED, key,
					value.getClass().getSimpleName(), clazz.getSimpleName());

		}
		catch (BusinessException e) {
			// 重新抛出业务异常
			throw e;
		}
		catch (Exception e) {
			log.error(ErrorMessages.Cache.CACHE_TYPE_CONVERSION_FAILED, key, value, value.getClass().getSimpleName(),
					clazz.getSimpleName(), e);
			throw new BusinessException(ErrorMessages.Cache.CACHE_TYPE_CONVERSION_FAILED, e, key, value,
					value.getClass().getSimpleName(), clazz.getSimpleName());
		}
	}

	/**
	 * 数值类型转换
	 */
	@SuppressWarnings("unchecked")
	private <T> T convertNumber(Number numberValue, Class<T> clazz) {
		return switch (clazz.getSimpleName()) {
			case "Long" -> (T) Long.valueOf(numberValue.longValue());
			case "Integer" -> (T) Integer.valueOf(numberValue.intValue());
			case "Double" -> (T) Double.valueOf(numberValue.doubleValue());
			case "Float" -> (T) Float.valueOf(numberValue.floatValue());
			case "Short" -> (T) Short.valueOf(numberValue.shortValue());
			case "Byte" -> (T) Byte.valueOf(numberValue.byteValue());
			default -> null;
		};
	}

	/**
	 * 字符串到数值类型转换
	 */
	@SuppressWarnings("unchecked")
	private <T> T convertStringToNumber(String stringValue, Class<T> clazz) {
		return switch (clazz.getSimpleName()) {
			case "Long" -> (T) Long.valueOf(stringValue);
			case "Integer" -> (T) Integer.valueOf(stringValue);
			case "Double" -> (T) Double.valueOf(stringValue);
			case "Float" -> (T) Float.valueOf(stringValue);
			case "Short" -> (T) Short.valueOf(stringValue);
			case "Byte" -> (T) Byte.valueOf(stringValue);
			default -> null;
		};
	}

	/**
	 * 转换为布尔类型
	 */
	@SuppressWarnings("unchecked")
	private <T> T convertToBoolean(Object value) {
		if (value instanceof Number numberValue) {
			return (T) Boolean.valueOf(numberValue.intValue() != 0);
		}
		if (value instanceof String stringValue) {
			return (T) Boolean.valueOf("true".equalsIgnoreCase(stringValue) || "1".equals(stringValue)
					|| "yes".equalsIgnoreCase(stringValue));
		}
		return (T) Boolean.valueOf(value.toString());
	}

	/**
	 * 执行Redis操作并处理异常
	 * @param operation 操作
	 * @param errorMessage 错误消息模板
	 * @param params 错误消息参数
	 */
	private void executeWithExceptionHandling(Runnable operation, String errorMessage, Object... params) {
		try {
			operation.run();
		}
		catch (Exception e) {
			log.error(errorMessage, params, e);
			throw new BusinessException(errorMessage, e, params);
		}
	}

}
