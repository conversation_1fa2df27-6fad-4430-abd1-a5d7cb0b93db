package com.ai.cutover.common.result;

import lombok.Data;

/**
 * 统一响应结果类 用于封装所有API接口的返回结果
 *
 * <AUTHOR>
 */
@Data
public class Result<T> {

	/**
	 * 响应状态码
	 */
	private Integer code;

	/**
	 * 响应消息
	 */
	private String message;

	/**
	 * 响应数据
	 */
	private T data;

	/**
	 * 时间戳
	 */
	private Long timestamp;

	public Result() {
		this.timestamp = System.currentTimeMillis();
	}

	public Result(Integer code, String message, T data) {
		this();
		this.code = code;
		this.message = message;
		this.data = data;
	}

	/**
	 * 成功响应（无数据）
	 */
	public static <T> Result<T> success() {
		return new Result<>(200, "操作成功", null);
	}

	/**
	 * 成功响应（带数据）
	 */
	public static <T> Result<T> success(T data) {
		return new Result<>(200, "操作成功", data);
	}

	/**
	 * 成功响应（自定义消息）
	 */
	public static <T> Result<T> success(String message, T data) {
		return new Result<>(200, message, data);
	}

	/**
	 * 失败响应
	 */
	public static <T> Result<T> error(String message) {
		return new Result<>(500, message, null);
	}

	/**
	 * 失败响应（自定义状态码）
	 */
	public static <T> Result<T> error(Integer code, String message) {
		return new Result<>(code, message, null);
	}

	/**
	 * 参数错误响应
	 */
	public static <T> Result<T> badRequest(String message) {
		return new Result<>(400, message, null);
	}

	/**
	 * 未授权响应
	 */
	public static <T> Result<T> unauthorized(String message) {
		return new Result<>(401, message, null);
	}

	/**
	 * 禁止访问响应
	 */
	public static <T> Result<T> forbidden(String message) {
		return new Result<>(403, message, null);
	}

	/**
	 * 资源未找到响应
	 */
	public static <T> Result<T> notFound(String message) {
		return new Result<>(404, message, null);
	}

}
