package com.ai.cutover.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 资源类型枚举 用于断言方法中的资源类型标识
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum ResourceType {

	// 系统管理模块
	USER("用户"), ROLE("角色"), PERMISSION("权限"), MENU("菜单"), DEPT("部门"),

	// 流程管理模块
	PROCESS_DEFINITION("流程定义"), PROCESS_INSTANCE("流程实例"), TASK("任务"),

	// 切换管理模块
	CUTOVER_PLAN("切换计划"), CUTOVER_TASK("切换任务"),

	// 通用资源
	FILE("文件"), DATA("数据"), RECORD("记录"), CONFIG("配置");

	/**
	 * 资源名称
	 */
	private final String name;

	/**
	 * 获取不存在的错误消息
	 */
	public String getNotFoundMessage() {
		return name + "不存在";
	}

	/**
	 * 获取已存在的错误消息
	 */
	public String getAlreadyExistsMessage() {
		return name + "已存在";
	}

	/**
	 * 获取状态无效的错误消息
	 */
	public String getInvalidStatusMessage(String currentStatus) {
		return name + "状态无效，当前状态：" + currentStatus;
	}

	/**
	 * 获取无权限操作的错误消息
	 */
	public String getNoPermissionMessage(String operation) {
		return "无权限执行" + name + operation + "操作";
	}

}
