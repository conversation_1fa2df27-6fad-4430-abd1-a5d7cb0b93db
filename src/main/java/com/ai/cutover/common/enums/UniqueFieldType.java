package com.ai.cutover.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 唯一字段类型枚举 用于唯一性约束断言中的字段类型标识
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum UniqueFieldType {

	// 用户相关字段
	USERNAME("用户名"), EMAIL("邮箱"), PHONE("手机号"),

	// 角色相关字段
	ROLE_CODE("角色编码"), ROLE_NAME("角色名称"),

	// 权限相关字段
	PERMISSION_CODE("权限编码"), PERMISSION_NAME("权限名称"),

	// 菜单相关字段
	MENU_CODE("菜单编码"), MENU_NAME("菜单名称"),

	// 部门相关字段
	DEPT_CODE("部门编码"), DEPT_NAME("部门名称"),

	// 流程相关字段
	PROCESS_KEY("流程标识"), PROCESS_NAME("流程名称"),

	// 通用字段
	CODE("编码"), NAME("名称"), TITLE("标题"), KEY("标识");

	/**
	 * 字段名称
	 */
	private final String name;

	/**
	 * 获取唯一性约束错误消息
	 */
	public String getUniqueConstraintMessage(Object value) {
		return name + "[" + value + "]已存在";
	}

}
