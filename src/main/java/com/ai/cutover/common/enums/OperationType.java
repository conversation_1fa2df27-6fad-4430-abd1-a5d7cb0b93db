package com.ai.cutover.common.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 操作类型枚举 用于权限检查断言中的操作类型标识
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum OperationType {

	// CRUD 操作
	CREATE("创建"), READ("查看"), UPDATE("修改"), DELETE("删除"),

	// 批量操作
	BATCH_CREATE("批量创建"), BATCH_UPDATE("批量修改"), BATCH_DELETE("批量删除"),

	// 状态操作
	ENABLE("启用"), DISABLE("禁用"), LOCK("锁定"), UNLOCK("解锁"),

	// 权限操作
	ASSIGN("分配"), REVOKE("撤销"),

	// 流程操作
	START("启动"), APPROVE("审批"), REJECT("拒绝"), COMPLETE("完成"), TERMINATE("终止"),

	// 系统操作
	EXPORT("导出"), IMPORT("导入"), BACKUP("备份"), RESTORE("恢复"),

	// 切换操作
	EXECUTE("执行"), ROLLBACK("回滚"), MONITOR("监控");

	/**
	 * 操作名称
	 */
	private final String name;

	/**
	 * 获取无权限操作的错误消息
	 */
	public String getNoPermissionMessage() {
		return "无权限执行" + name + "操作";
	}

}
