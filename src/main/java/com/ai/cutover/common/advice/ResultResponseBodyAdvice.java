package com.ai.cutover.common.advice;

import com.ai.cutover.common.annotation.NoResultWrapper;
import com.ai.cutover.common.result.Result;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 统一响应体包装处理器 自动将Controller返回值包装为Result格式
 *
 * <AUTHOR>
 */
@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
public class ResultResponseBodyAdvice implements ResponseBodyAdvice<Object> {

	private final ObjectMapper objectMapper;

	@Override
	public boolean supports(MethodParameter returnType, Class<? extends HttpMessageConverter<?>> converterType) {
		// 检查方法是否标记了@NoResultWrapper注解
		if (returnType.hasMethodAnnotation(NoResultWrapper.class)) {
			return false;
		}

		// 检查类是否标记了@NoResultWrapper注解
		if (returnType.getDeclaringClass().isAnnotationPresent(NoResultWrapper.class)) {
			return false;
		}

		// 如果返回值已经是Result类型，不需要再包装
		if (returnType.getParameterType().equals(Result.class)) {
			return false;
		}

		// 排除Swagger相关接口
		String className = returnType.getDeclaringClass().getName();
		if (className.contains("springfox") || className.contains("swagger") || className.contains("springdoc")
				|| className.contains("knife4j") || className.contains("OpenApiResource")
				|| className.contains("ApiDocsController") || className.contains("OpenApiWebMvcResource")
				|| className.contains("SwaggerUiWebMvcConfigurer")) {
			return false;
		}

		// 排除Spring Boot Actuator接口
		if (className.contains("actuator")) {
			return false;
		}

		// 排除错误页面
		if (className.contains("BasicErrorController")) {
			return false;
		}

		return true;
	}

	@Override
	public Object beforeBodyWrite(Object body, MethodParameter returnType, MediaType selectedContentType,
			Class<? extends HttpMessageConverter<?>> selectedConverterType, ServerHttpRequest request,
			ServerHttpResponse response) {

		// 如果body已经是Result类型，直接返回
		if (body instanceof Result) {
			return body;
		}

		// 包装为Result
		Result<Object> result = Result.success(body);

		// 特殊处理String类型返回值，避免类型转换异常
		if (returnType.getParameterType().equals(String.class)) {
			try {
				return objectMapper.writeValueAsString(result);
			}
			catch (JsonProcessingException e) {
				log.error("JSON序列化失败", e);
				return objectMapper.valueToTree(result);
			}
		}

		return result;
	}

}
