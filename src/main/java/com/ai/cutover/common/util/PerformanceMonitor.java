package com.ai.cutover.common.util;

import lombok.extern.slf4j.Slf4j;
import lombok.experimental.UtilityClass;

import java.util.function.Supplier;

/**
 * 性能监控工具类 提供方法执行时间监控功能
 *
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class PerformanceMonitor {

	/**
	 * 监控方法执行时间
	 * @param operation 操作名称
	 * @param supplier 执行的操作
	 * @param <T> 返回类型
	 * @return 操作结果
	 */
	public static <T> T monitor(String operation, Supplier<T> supplier) {
		long startTime = System.currentTimeMillis();
		try {
			T result = supplier.get();
			long endTime = System.currentTimeMillis();
			long duration = endTime - startTime;

			if (duration > 1000) {
				log.warn("慢操作警告: {} 耗时 {}ms", operation, duration);
			}
			else if (log.isDebugEnabled()) {
				log.debug("操作完成: {} 耗时 {}ms", operation, duration);
			}

			return result;
		}
		catch (Exception e) {
			long endTime = System.currentTimeMillis();
			long duration = endTime - startTime;
			log.error("操作失败: {} 耗时 {}ms", operation, duration, e);
			throw e;
		}
	}

	/**
	 * 监控无返回值方法执行时间
	 * @param operation 操作名称
	 * @param runnable 执行的操作
	 */
	public static void monitor(String operation, Runnable runnable) {
		monitor(operation, () -> {
			runnable.run();
			return null;
		});
	}

	/**
	 * 监控方法执行时间（带阈值）
	 * @param operation 操作名称
	 * @param supplier 执行的操作
	 * @param warningThreshold 警告阈值（毫秒）
	 * @param <T> 返回类型
	 * @return 操作结果
	 */
	public static <T> T monitor(String operation, Supplier<T> supplier, long warningThreshold) {
		long startTime = System.currentTimeMillis();
		try {
			T result = supplier.get();
			long endTime = System.currentTimeMillis();
			long duration = endTime - startTime;

			if (duration > warningThreshold) {
				log.warn("慢操作警告: {} 耗时 {}ms (阈值: {}ms)", operation, duration, warningThreshold);
			}
			else if (log.isDebugEnabled()) {
				log.debug("操作完成: {} 耗时 {}ms", operation, duration);
			}

			return result;
		}
		catch (Exception e) {
			long endTime = System.currentTimeMillis();
			long duration = endTime - startTime;
			log.error("操作失败: {} 耗时 {}ms", operation, duration, e);
			throw e;
		}
	}

	/**
	 * 简单的执行时间测量
	 * @param operation 操作名称
	 * @param supplier 执行的操作
	 * @param <T> 返回类型
	 * @return 包含结果和执行时间的对象
	 */
	public static <T> TimedResult<T> time(String operation, Supplier<T> supplier) {
		long startTime = System.currentTimeMillis();
		try {
			T result = supplier.get();
			long duration = System.currentTimeMillis() - startTime;
			return new TimedResult<>(result, duration, null);
		}
		catch (Exception e) {
			long duration = System.currentTimeMillis() - startTime;
			return new TimedResult<>(null, duration, e);
		}
	}

	/**
	 * 带时间结果的包装类
	 */
	public static class TimedResult<T> {

		private final T result;

		private final long duration;

		private final Exception exception;

		public TimedResult(T result, long duration, Exception exception) {
			this.result = result;
			this.duration = duration;
			this.exception = exception;
		}

		public T getResult() {
			if (exception != null) {
				if (exception instanceof RuntimeException) {
					throw (RuntimeException) exception;
				}
				throw new RuntimeException(exception);
			}
			return result;
		}

		public long getDuration() {
			return duration;
		}

		public boolean hasException() {
			return exception != null;
		}

		public Exception getException() {
			return exception;
		}

	}

}
