package com.ai.cutover.common.util;

import cn.hutool.core.util.StrUtil;
import jakarta.servlet.http.HttpServletRequest;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

/**
 * Web工具类 提供Web相关的通用工具方法
 *
 * <AUTHOR>
 */
@Slf4j
@UtilityClass
public class WebUtils {

	/**
	 * 获取客户端真实IP地址 支持通过代理、负载均衡等方式获取真实IP
	 * @param request HTTP请求对象
	 * @return 客户端IP地址
	 */
	public static String getClientIpAddress(HttpServletRequest request) {
		if (request == null) {
			log.warn("HttpServletRequest为空，无法获取客户端IP");
			return "unknown";
		}

		// 1. 检查X-Forwarded-For头（通过代理服务器时使用）
		String xForwardedFor = request.getHeader("X-Forwarded-For");
		if (isValidIp(xForwardedFor)) {
			// X-Forwarded-For可能包含多个IP，取第一个
			String firstIp = xForwardedFor.split(",")[0].trim();
			log.debug("通过X-Forwarded-For获取到IP: {}", firstIp);
			return firstIp;
		}

		// 2. 检查X-Real-IP头（Nginx代理常用）
		String xRealIp = request.getHeader("X-Real-IP");
		if (isValidIp(xRealIp)) {
			log.debug("通过X-Real-IP获取到IP: {}", xRealIp);
			return xRealIp;
		}

		// 3. 检查X-Original-Forwarded-For头
		String xOriginalForwardedFor = request.getHeader("X-Original-Forwarded-For");
		if (isValidIp(xOriginalForwardedFor)) {
			String firstIp = xOriginalForwardedFor.split(",")[0].trim();
			log.debug("通过X-Original-Forwarded-For获取到IP: {}", firstIp);
			return firstIp;
		}

		// 4. 检查Proxy-Client-IP头
		String proxyClientIp = request.getHeader("Proxy-Client-IP");
		if (isValidIp(proxyClientIp)) {
			log.debug("通过Proxy-Client-IP获取到IP: {}", proxyClientIp);
			return proxyClientIp;
		}

		// 5. 检查WL-Proxy-Client-IP头（WebLogic代理）
		String wlProxyClientIp = request.getHeader("WL-Proxy-Client-IP");
		if (isValidIp(wlProxyClientIp)) {
			log.debug("通过WL-Proxy-Client-IP获取到IP: {}", wlProxyClientIp);
			return wlProxyClientIp;
		}

		// 6. 检查HTTP_CLIENT_IP头
		String httpClientIp = request.getHeader("HTTP_CLIENT_IP");
		if (isValidIp(httpClientIp)) {
			log.debug("通过HTTP_CLIENT_IP获取到IP: {}", httpClientIp);
			return httpClientIp;
		}

		// 7. 检查HTTP_X_FORWARDED_FOR头
		String httpXForwardedFor = request.getHeader("HTTP_X_FORWARDED_FOR");
		if (isValidIp(httpXForwardedFor)) {
			String firstIp = httpXForwardedFor.split(",")[0].trim();
			log.debug("通过HTTP_X_FORWARDED_FOR获取到IP: {}", firstIp);
			return firstIp;
		}

		// 8. 最后使用getRemoteAddr()方法获取
		String remoteAddr = request.getRemoteAddr();
		log.debug("通过getRemoteAddr()获取到IP: {}", remoteAddr);
		return remoteAddr != null ? remoteAddr : "unknown";
	}

	/**
	 * 检查IP地址是否有效
	 * @param ip IP地址字符串
	 * @return 是否为有效IP
	 */
	private static boolean isValidIp(String ip) {
		return StrUtil.isNotBlank(ip) && !"unknown".equalsIgnoreCase(ip) && !"null".equalsIgnoreCase(ip);
	}

	/**
	 * 获取用户代理字符串
	 * @param request HTTP请求对象
	 * @return 用户代理字符串
	 */
	public static String getUserAgent(HttpServletRequest request) {
		if (request == null) {
			return "unknown";
		}
		String userAgent = request.getHeader("User-Agent");
		return StrUtil.isNotBlank(userAgent) ? userAgent : "unknown";
	}

	/**
	 * 获取请求的完整URL
	 * @param request HTTP请求对象
	 * @return 完整的请求URL
	 */
	public static String getFullRequestUrl(HttpServletRequest request) {
		if (request == null) {
			return "";
		}

		StringBuffer requestUrl = request.getRequestURL();
		String queryString = request.getQueryString();

		if (StrUtil.isNotBlank(queryString)) {
			requestUrl.append("?").append(queryString);
		}

		return requestUrl.toString();
	}

	/**
	 * 判断是否为Ajax请求
	 * @param request HTTP请求对象
	 * @return 是否为Ajax请求
	 */
	public static boolean isAjaxRequest(HttpServletRequest request) {
		if (request == null) {
			return false;
		}

		String requestedWith = request.getHeader("X-Requested-With");
		return "XMLHttpRequest".equalsIgnoreCase(requestedWith);
	}

	/**
	 * 判断是否为移动端请求
	 * @param request HTTP请求对象
	 * @return 是否为移动端请求
	 */
	public static boolean isMobileRequest(HttpServletRequest request) {
		if (request == null) {
			return false;
		}

		String userAgent = getUserAgent(request).toLowerCase();
		return userAgent.contains("mobile") || userAgent.contains("android") || userAgent.contains("iphone")
				|| userAgent.contains("ipad") || userAgent.contains("windows phone");
	}

}
