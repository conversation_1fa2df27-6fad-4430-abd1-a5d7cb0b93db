package com.ai.cutover.common.util;

import cn.hutool.core.collection.CollUtil;
import lombok.experimental.UtilityClass;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 缓存工具类 提供常用的缓存操作和批量查询优化方法
 *
 * <AUTHOR>
 */
@UtilityClass
public class CacheUtil {

	/**
	 * 批量查询并构建映射关系，避免N+1查询问题
	 * @param ids ID集合
	 * @param batchQuery 批量查询函数
	 * @param keyExtractor 键提取器
	 * @param valueExtractor 值提取器
	 * @param <ID> ID类型
	 * @param <ENTITY> 实体类型
	 * @param <VALUE> 值类型
	 * @return ID到值的映射
	 */
	public static <ID, ENTITY, VALUE> Map<ID, VALUE> batchQueryToMap(Collection<ID> ids,
			Function<List<ID>, List<ENTITY>> batchQuery, Function<ENTITY, ID> keyExtractor,
			Function<ENTITY, VALUE> valueExtractor) {

		if (CollUtil.isEmpty(ids)) {
			return Map.of();
		}

		List<ID> distinctIds = ids.stream().distinct().collect(Collectors.toList());
		List<ENTITY> entities = batchQuery.apply(distinctIds);

		return entities.stream().collect(Collectors.toMap(keyExtractor, valueExtractor));
	}

	/**
	 * 批量查询并构建实体映射关系
	 * @param ids ID集合
	 * @param batchQuery 批量查询函数
	 * @param keyExtractor 键提取器
	 * @param <ID> ID类型
	 * @param <ENTITY> 实体类型
	 * @return ID到实体的映射
	 */
	public static <ID, ENTITY> Map<ID, ENTITY> batchQueryToEntityMap(Collection<ID> ids,
			Function<List<ID>, List<ENTITY>> batchQuery, Function<ENTITY, ID> keyExtractor) {

		return batchQueryToMap(ids, batchQuery, keyExtractor, Function.identity());
	}

	/**
	 * 生成缓存键
	 * @param prefix 前缀
	 * @param suffix 后缀
	 * @return 缓存键
	 */
	public static String generateKey(String prefix, Object suffix) {
		return prefix + ":" + suffix;
	}

	/**
	 * 生成缓存键
	 * @param prefix 前缀
	 * @param middle 中间部分
	 * @param suffix 后缀
	 * @return 缓存键
	 */
	public static String generateKey(String prefix, Object middle, Object suffix) {
		return prefix + ":" + middle + ":" + suffix;
	}

	/**
	 * 批量生成缓存键
	 * @param prefix 前缀
	 * @param suffixes 后缀集合
	 * @return 缓存键列表
	 */
	public static List<String> generateKeys(String prefix, Collection<?> suffixes) {
		return suffixes.stream().map(suffix -> generateKey(prefix, suffix)).collect(Collectors.toList());
	}

}
