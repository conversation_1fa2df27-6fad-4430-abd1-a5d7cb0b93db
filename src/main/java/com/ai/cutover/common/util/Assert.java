package com.ai.cutover.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ai.cutover.common.constant.ErrorMessages;
import com.ai.cutover.common.constant.StatusConstants;
import com.ai.cutover.common.enums.UniqueFieldType;
import com.ai.cutover.common.enums.OperationType;
import com.ai.cutover.common.enums.ResourceType;
import com.ai.cutover.common.exception.BusinessException;
import lombok.experimental.UtilityClass;

import java.util.Collection;
import java.util.Objects;
import java.util.function.Supplier;

/**
 * 业务断言工具类 提供简洁的断言方法，失败时抛出BusinessException
 *
 * <AUTHOR>
 */
@UtilityClass
public class Assert {

	/**
	 * 断言对象不为null
	 * @param object 待检查对象
	 * @param message 错误消息
	 * @throws BusinessException 如果对象为null
	 */
	public static void notNull(Object object, String message) {
		if (object == null) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 断言对象不为null（支持占位符）
	 * @param object 待检查对象
	 * @param messageTemplate 错误消息模板
	 * @param params 参数
	 * @throws BusinessException 如果对象为null
	 */
	public static void notNull(Object object, String messageTemplate, Object... params) {
		if (object == null) {
			throw new BusinessException(messageTemplate, params);
		}
	}

	/**
	 * 断言对象为null
	 * @param object 待检查对象
	 * @param message 错误消息
	 * @throws BusinessException 如果对象不为null
	 */
	public static void isNull(Object object, String message) {
		if (object != null) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 断言字符串不为空（null、空字符串、只包含空白字符）
	 * @param text 待检查字符串
	 * @param message 错误消息
	 * @throws BusinessException 如果字符串为空
	 */
	public static void hasText(String text, String message) {
		if (StrUtil.isBlank(text)) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 断言字符串不为null且不为空字符串
	 * @param text 待检查字符串
	 * @param message 错误消息
	 * @throws BusinessException 如果字符串为null或空字符串
	 */
	public static void hasLength(String text, String message) {
		if (StrUtil.isEmpty(text)) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 断言集合不为空
	 * @param collection 待检查集合
	 * @param message 错误消息
	 * @throws BusinessException 如果集合为null或空
	 */
	public static void notEmpty(Collection<?> collection, String message) {
		if (CollUtil.isEmpty(collection)) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 断言集合为空
	 * @param collection 待检查集合
	 * @param message 错误消息
	 * @throws BusinessException 如果集合不为空
	 */
	public static void isEmpty(Collection<?> collection, String message) {
		if (CollUtil.isNotEmpty(collection)) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 断言数组不为空
	 * @param array 待检查数组
	 * @param message 错误消息
	 * @throws BusinessException 如果数组为null或空
	 */
	public static void notEmpty(Object[] array, String message) {
		if (array == null || array.length == 0) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 断言表达式为true
	 * @param expression 待检查表达式
	 * @param message 错误消息
	 * @throws BusinessException 如果表达式为false
	 */
	public static void isTrue(boolean expression, String message) {
		if (!expression) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 断言表达式为true（支持占位符）
	 * @param expression 待检查表达式
	 * @param messageTemplate 错误消息模板
	 * @param params 参数
	 * @throws BusinessException 如果表达式为false
	 */
	public static void isTrue(boolean expression, String messageTemplate, Object... params) {
		if (!expression) {
			throw new BusinessException(messageTemplate, params);
		}
	}

	/**
	 * 断言表达式为false
	 * @param expression 待检查表达式
	 * @param message 错误消息
	 * @throws BusinessException 如果表达式为true
	 */
	public static void isFalse(boolean expression, String message) {
		if (expression) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 断言表达式为false（支持占位符）
	 * @param expression 待检查表达式
	 * @param messageTemplate 错误消息模板
	 * @param params 参数
	 * @throws BusinessException 如果表达式为true
	 */
	public static void isFalse(boolean expression, String messageTemplate, Object... params) {
		if (expression) {
			throw new BusinessException(messageTemplate, params);
		}
	}

	/**
	 * 断言两个对象相等
	 * @param expected 期望值
	 * @param actual 实际值
	 * @param message 错误消息
	 * @throws BusinessException 如果两个对象不相等
	 */
	public static void equals(Object expected, Object actual, String message) {
		if (!Objects.equals(expected, actual)) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 断言两个对象不相等
	 * @param unexpected 不期望的值
	 * @param actual 实际值
	 * @param message 错误消息
	 * @throws BusinessException 如果两个对象相等
	 */
	public static void notEquals(Object unexpected, Object actual, String message) {
		if (Objects.equals(unexpected, actual)) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 断言数字大于指定值
	 * @param actual 实际值
	 * @param threshold 阈值
	 * @param messageTemplate 错误消息模板
	 * @param params 参数
	 * @throws BusinessException 如果实际值不大于阈值
	 */
	public static void greaterThan(Number actual, Number threshold, String messageTemplate, Object... params) {
		if (actual == null || threshold == null || actual.doubleValue() <= threshold.doubleValue()) {
			throw new BusinessException(messageTemplate, params);
		}
	}

	/**
	 * 断言数字大于等于指定值
	 * @param actual 实际值
	 * @param threshold 阈值
	 * @param messageTemplate 错误消息模板
	 * @param params 参数
	 * @throws BusinessException 如果实际值小于阈值
	 */
	public static void greaterThanOrEqual(Number actual, Number threshold, String messageTemplate, Object... params) {
		if (actual == null || threshold == null || actual.doubleValue() < threshold.doubleValue()) {
			throw new BusinessException(messageTemplate, params);
		}
	}

	/**
	 * 断言数字小于指定值
	 * @param actual 实际值
	 * @param threshold 阈值
	 * @param messageTemplate 错误消息模板
	 * @param params 参数
	 * @throws BusinessException 如果实际值不小于阈值
	 */
	public static void lessThan(Number actual, Number threshold, String messageTemplate, Object... params) {
		if (actual == null || threshold == null || actual.doubleValue() >= threshold.doubleValue()) {
			throw new BusinessException(messageTemplate, params);
		}
	}

	/**
	 * 断言数字小于等于指定值
	 * @param actual 实际值
	 * @param threshold 阈值
	 * @param messageTemplate 错误消息模板
	 * @param params 参数
	 * @throws BusinessException 如果实际值大于阈值
	 */
	public static void lessThanOrEqual(Number actual, Number threshold, String messageTemplate, Object... params) {
		if (actual == null || threshold == null || actual.doubleValue() > threshold.doubleValue()) {
			throw new BusinessException(messageTemplate, params);
		}
	}

	/**
	 * 断言字符串匹配正则表达式
	 * @param text 待检查字符串
	 * @param pattern 正则表达式
	 * @param message 错误消息
	 * @throws BusinessException 如果字符串不匹配正则表达式
	 */
	public static void matches(String text, String pattern, String message) {
		if (text == null || !text.matches(pattern)) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 断言字符串匹配正则表达式（支持占位符）
	 * @param text 待检查字符串
	 * @param pattern 正则表达式
	 * @param messageTemplate 错误消息模板
	 * @param params 参数
	 * @throws BusinessException 如果字符串不匹配正则表达式
	 */
	public static void matches(String text, String pattern, String messageTemplate, Object... params) {
		if (text == null || !text.matches(pattern)) {
			throw new BusinessException(messageTemplate, params);
		}
	}

	/**
	 * 断言对象是指定类型的实例
	 * @param type 期望类型
	 * @param obj 待检查对象
	 * @param message 错误消息
	 * @throws BusinessException 如果对象不是指定类型的实例
	 */
	public static void isInstanceOf(Class<?> type, Object obj, String message) {
		notNull(type, ErrorMessages.General.PARAM_NOT_NULL);
		if (!type.isInstance(obj)) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 断言对象可以赋值给指定类型
	 * @param superType 父类型
	 * @param subType 子类型
	 * @param message 错误消息
	 * @throws BusinessException 如果子类型不能赋值给父类型
	 */
	public static void isAssignable(Class<?> superType, Class<?> subType, String message) {
		notNull(superType, ErrorMessages.General.PARAM_NOT_NULL);
		if (subType == null || !superType.isAssignableFrom(subType)) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 直接抛出业务异常
	 * @param message 错误消息
	 * @throws BusinessException 业务异常
	 */
	public static void fail(String message) {
		throw new BusinessException(message);
	}

	/**
	 * 直接抛出业务异常（支持占位符）
	 * @param messageTemplate 错误消息模板
	 * @param params 参数
	 * @throws BusinessException 业务异常
	 */
	public static void fail(String messageTemplate, Object... params) {
		throw new BusinessException(messageTemplate, params);
	}

	/**
	 * 根据条件抛出业务异常
	 * @param condition 条件
	 * @param message 错误消息
	 * @throws BusinessException 如果条件为true则抛出异常
	 */
	public static void failIf(boolean condition, String message) {
		if (condition) {
			throw new BusinessException(message);
		}
	}

	/**
	 * 根据条件抛出业务异常（支持占位符）
	 * @param condition 条件
	 * @param messageTemplate 错误消息模板
	 * @param params 参数
	 * @throws BusinessException 如果条件为true则抛出异常
	 */
	public static void failIf(boolean condition, String messageTemplate, Object... params) {
		if (condition) {
			throw new BusinessException(messageTemplate, params);
		}
	}

	// ==================== 业务相关断言方法 ====================

	/**
	 * 断言资源存在，否则抛出业务异常
	 * @param resource 资源
	 * @param resourceType 资源类型
	 */
	public static void resourceExists(Object resource, ResourceType resourceType) {
		notNull(resource, resourceType.getNotFoundMessage());
	}

	/**
	 * 断言资源不存在，否则抛出业务异常
	 * @param resource 资源
	 * @param resourceType 资源类型
	 */
	public static void resourceNotExists(Object resource, ResourceType resourceType) {
		isNull(resource, resourceType.getAlreadyExistsMessage());
	}

	/**
	 * 断言业务状态正确，否则抛出业务异常
	 * @param condition 业务条件
	 * @param messageSupplier 错误消息提供者
	 */
	public static void businessState(boolean condition, Supplier<String> messageSupplier) {
		if (!condition) {
			throw new BusinessException(messageSupplier.get());
		}
	}

	/**
	 * 断言权限检查通过，否则抛出业务异常
	 * @param hasPermission 是否有权限
	 * @param operationType 操作类型
	 */
	public static void hasPermission(boolean hasPermission, OperationType operationType) {
		isTrue(hasPermission, operationType.getNoPermissionMessage());
	}

	/**
	 * 断言状态有效，否则抛出业务异常
	 * @param status 状态
	 * @param validStatus 有效状态
	 * @param resourceType 资源类型
	 */
	public static void validStatus(String status, String validStatus, ResourceType resourceType) {
		isTrue(validStatus.equals(status), resourceType.getInvalidStatusMessage(status));
	}

	/**
	 * 断言唯一性约束，否则抛出业务异常
	 * @param exists 是否已存在
	 * @param fieldType 唯一字段类型
	 * @param fieldValue 字段值
	 */
	public static void uniqueConstraint(boolean exists, UniqueFieldType fieldType, Object fieldValue) {
		isFalse(exists, fieldType.getUniqueConstraintMessage(fieldValue));
	}

	/**
	 * 断言用户状态正常
	 * @param userStatus 用户状态
	 */
	public static void userStatusNormal(String userStatus) {
		validStatus(userStatus, StatusConstants.User.NORMAL, ResourceType.USER);
	}

	/**
	 * 断言角色状态正常
	 * @param roleStatus 角色状态
	 */
	public static void roleStatusNormal(String roleStatus) {
		validStatus(roleStatus, StatusConstants.Role.NORMAL, ResourceType.ROLE);
	}

}
