package com.ai.cutover.common.constant;

import lombok.experimental.UtilityClass;

/**
 * 缓存常量类 统一管理缓存相关的常量
 *
 * <AUTHOR>
 */
@UtilityClass
public class CacheConstants {

	/**
	 * 缓存键前缀
	 */
	@UtilityClass
	public static class Key {

		/** 系统模块缓存前缀 */
		@UtilityClass
		public static class System {

			/** 用户缓存前缀 */
			public static final String USER = "system:user:";

			/** 角色缓存前缀 */
			public static final String ROLE = "system:role:";

			/** 部门缓存前缀 */
			public static final String DEPT = "system:dept:";

			/** 菜单缓存前缀 */
			public static final String MENU = "system:menu:";

			/** 权限缓存前缀 */
			public static final String PERMISSION = "system:permission:";

			/** 用户权限缓存 */
			public static final String USER_PERMISSIONS = "system:user:permissions:";

			/** 用户角色缓存 */
			public static final String USER_ROLES = "system:user:roles:";

		}

		/** 认证模块缓存前缀 */
		@UtilityClass
		public static class Auth {

			/** 验证码缓存前缀 */
			public static final String CAPTCHA = "auth:captcha:";

			/** 验证码错误次数前缀 */
			public static final String CAPTCHA_ERROR_COUNT = "auth:captcha:error:count:";

			/** 登录失败次数前缀 */
			public static final String LOGIN_FAIL_COUNT = "auth:login:fail:count:";

			/** 登录锁定前缀 */
			public static final String LOGIN_LOCK = "auth:login:lock:";

			/** 用户会话前缀 */
			public static final String USER_SESSION = "auth:session:";

			/** 刷新令牌前缀 */
			public static final String REFRESH_TOKEN = "auth:refresh:token:";

		}

		/** 流程模块缓存前缀 */
		@UtilityClass
		public static class Process {

			/** 流程定义缓存前缀 */
			public static final String DEFINITION = "process:definition:";

			/** 流程实例缓存前缀 */
			public static final String INSTANCE = "process:instance:";

			/** 任务缓存前缀 */
			public static final String TASK = "process:task:";

		}

		/** 切换模块缓存前缀 */
		@UtilityClass
		public static class Cutover {

			/** 切换计划缓存前缀 */
			public static final String PLAN = "cutover:plan:";

			/** 切换任务缓存前缀 */
			public static final String TASK = "cutover:task:";

			/** 切换状态缓存前缀 */
			public static final String STATUS = "cutover:status:";

		}

	}

	/**
	 * 缓存过期时间（秒）
	 */
	@UtilityClass
	public static class TTL {

		/** 1分钟 */
		public static final long ONE_MINUTE = 60L;

		/** 5分钟 */
		public static final long FIVE_MINUTES = 300L;

		/** 10分钟 */
		public static final long TEN_MINUTES = 600L;

		/** 30分钟 */
		public static final long THIRTY_MINUTES = 1800L;

		/** 1小时 */
		public static final long ONE_HOUR = 3600L;

		/** 2小时 */
		public static final long TWO_HOURS = 7200L;

		/** 6小时 */
		public static final long SIX_HOURS = 21600L;

		/** 12小时 */
		public static final long TWELVE_HOURS = 43200L;

		/** 1天 */
		public static final long ONE_DAY = 86400L;

		/** 7天 */
		public static final long ONE_WEEK = 604800L;

		/** 30天 */
		public static final long ONE_MONTH = 2592000L;

	}

	/**
	 * 缓存配置
	 */
	@UtilityClass
	public static class Config {

		/** 验证码过期时间（分钟） */
		public static final int CAPTCHA_EXPIRE_MINUTES = 5;

		/** 登录锁定时间（分钟） */
		public static final int LOGIN_LOCK_MINUTES = 30;

		/** 会话超时时间（分钟） */
		public static final int SESSION_TIMEOUT_MINUTES = 120;

		/** 刷新令牌过期时间（天） */
		public static final int REFRESH_TOKEN_EXPIRE_DAYS = 7;

	}

	/**
	 * 缓存限制
	 */
	@UtilityClass
	public static class Limit {

		/** 最大登录失败次数 */
		public static final int MAX_LOGIN_FAIL_COUNT = 5;

		/** 最大验证码错误次数 */
		public static final int MAX_CAPTCHA_ERROR_COUNT = 3;

		/** 最大并发会话数 */
		public static final int MAX_CONCURRENT_SESSIONS = 1;

	}

}
