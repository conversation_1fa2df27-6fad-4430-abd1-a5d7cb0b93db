package com.ai.cutover.common.constant;

import lombok.experimental.UtilityClass;

/**
 * 业务常量类
 *
 * <AUTHOR>
 */
@UtilityClass
public class BusinessConstants {

	/**
	 * 菜单类型
	 */
	@UtilityClass
	public static class MenuType {

		/** 目录 */
		public static final String DIRECTORY = "DIRECTORY";

		/** 菜单 */
		public static final String MENU = "MENU";

		/** 按钮 */
		public static final String BUTTON = "BUTTON";

	}

	/**
	 * 权限类型
	 */
	@UtilityClass
	public static class PermissionType {

		/** 菜单权限 */
		public static final String MENU = "MENU";

		/** 按钮权限 */
		public static final String BUTTON = "BUTTON";

		/** API权限 */
		public static final String API = "API";

		/** 数据权限 */
		public static final String DATA = "DATA";

	}

	/**
	 * 性别
	 */
	@UtilityClass
	public static class Gender {

		/** 男性 */
		public static final String MALE = "MALE";

		/** 女性 */
		public static final String FEMALE = "FEMALE";

		/** 未知 */
		public static final String UNKNOWN = "UNKNOWN";

	}

	/**
	 * 是否标识
	 */
	@UtilityClass
	public static class YesNo {

		/** 是 */
		public static final String YES = "Y";

		/** 否 */
		public static final String NO = "N";

	}

	/**
	 * 数据权限范围
	 */
	@UtilityClass
	public static class DataScope {

		/** 全部数据 */
		public static final String ALL = "ALL";

		/** 本部门及子部门数据 */
		public static final String DEPT_AND_CHILD = "DEPT_AND_CHILD";

		/** 本部门数据 */
		public static final String DEPT_ONLY = "DEPT_ONLY";

		/** 仅本人数据 */
		public static final String SELF_ONLY = "SELF_ONLY";

		/** 自定义数据 */
		public static final String CUSTOM = "CUSTOM";

	}

	/**
	 * 系统内置角色
	 */
	@UtilityClass
	public static class BuiltInRole {

		/** 超级管理员 */
		public static final String SUPER_ADMIN = "SUPER_ADMIN";

		/** 系统管理员 */
		public static final String ADMIN = "ADMIN";

		/** 普通用户 */
		public static final String USER = "USER";

	}

	/**
	 * 系统内置用户
	 */
	@UtilityClass
	public static class BuiltInUser {

		/** 超级管理员 */
		public static final String SUPER_ADMIN = "superadmin";

		/** 系统管理员 */
		public static final String ADMIN = "admin";

		/** 系统用户 */
		public static final String SYSTEM = "system";

	}

	/**
	 * 默认值
	 */
	@UtilityClass
	public static class DefaultValue {

		/** 默认密码 */
		public static final String DEFAULT_PASSWORD = "123456";

		/** 默认头像 */
		public static final String DEFAULT_AVATAR = "/static/images/default-avatar.png";

		/** 根部门ID */
		public static final Long ROOT_DEPT_ID = 0L;

		/** 根部门祖级列表 */
		public static final String ROOT_DEPT_ANCESTORS = "0";

		/** 根菜单ID */
		public static final Long ROOT_MENU_ID = 0L;

		/** 默认排序 */
		public static final Integer DEFAULT_SORT = 0;

		/** 默认页码 */
		public static final Integer DEFAULT_PAGE_NUM = 1;

		/** 默认页大小 */
		public static final Integer DEFAULT_PAGE_SIZE = 10;

		/** 最大页大小 */
		public static final Integer MAX_PAGE_SIZE = 1000;

	}

	/**
	 * 文件相关
	 */
	@UtilityClass
	public static class File {

		/** 允许上传的图片类型 */
		public static final String[] ALLOWED_IMAGE_TYPES = { "jpg", "jpeg", "png", "gif", "bmp", "webp" };

		/** 允许上传的文档类型 */
		public static final String[] ALLOWED_DOCUMENT_TYPES = { "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
				"txt" };

		/** 最大文件大小（字节）- 10MB */
		public static final long MAX_FILE_SIZE = 10 * 1024 * 1024L;

		/** 最大图片大小（字节）- 5MB */
		public static final long MAX_IMAGE_SIZE = 5 * 1024 * 1024L;

	}

	/**
	 * 正则表达式
	 */
	@UtilityClass
	public static class Regex {

		/** 用户名正则 */
		public static final String USERNAME = "^[a-zA-Z0-9_]{4,20}$";

		/** 密码正则 */
		public static final String PASSWORD = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d@$!%*?&]{8,20}$";

		/** 邮箱正则 */
		public static final String EMAIL = "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$";

		/** 手机号正则 */
		public static final String PHONE = "^1[3-9]\\d{9}$";

		/** 身份证正则 */
		public static final String ID_CARD = "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$";

		/** IP地址正则 */
		public static final String IP_ADDRESS = "^((25[0-5]|2[0-4]\\d|[01]?\\d\\d?)\\.){3}(25[0-5]|2[0-4]\\d|[01]?\\d\\d?)$";

		/** URL正则 */
		public static final String URL = "^(https?|ftp)://[^\\s/$.?#].[^\\s]*$";

	}

	/**
	 * 系统配置键
	 */
	@UtilityClass
	public static class ConfigKey {

		/** 系统名称 */
		public static final String SYSTEM_NAME = "system.name";

		/** 系统版本 */
		public static final String SYSTEM_VERSION = "system.version";

		/** 系统Logo */
		public static final String SYSTEM_LOGO = "system.logo";

		/** 密码策略 */
		public static final String PASSWORD_POLICY = "system.password.policy";

		/** 会话超时时间 */
		public static final String SESSION_TIMEOUT = "system.session.timeout";

		/** 登录重试限制 */
		public static final String LOGIN_RETRY_LIMIT = "system.login.retry.limit";

		/** 文件上传路径 */
		public static final String FILE_UPLOAD_PATH = "system.file.upload.path";

		/** 文件上传大小限制 */
		public static final String FILE_UPLOAD_SIZE = "system.file.upload.size";

		/** 是否启用验证码 */
		public static final String CAPTCHA_ENABLED = "system.captcha.enabled";

		/** 是否启用注册 */
		public static final String REGISTER_ENABLED = "system.register.enabled";

	}

	/**
	 * 操作类型
	 */
	@UtilityClass
	public static class Operation {

		/** 创建 */
		public static final String CREATE = "CREATE";

		/** 更新 */
		public static final String UPDATE = "UPDATE";

		/** 删除 */
		public static final String DELETE = "DELETE";

		/** 查询 */
		public static final String QUERY = "QUERY";

		/** 导出 */
		public static final String EXPORT = "EXPORT";

		/** 导入 */
		public static final String IMPORT = "IMPORT";

		/** 重置密码 */
		public static final String RESET_PASSWORD = "RESET_PASSWORD";

		/** 修改状态 */
		public static final String CHANGE_STATUS = "CHANGE_STATUS";

		/** 分配权限 */
		public static final String ASSIGN_PERMISSION = "ASSIGN_PERMISSION";

		/** 分配角色 */
		public static final String ASSIGN_ROLE = "ASSIGN_ROLE";

	}

}
