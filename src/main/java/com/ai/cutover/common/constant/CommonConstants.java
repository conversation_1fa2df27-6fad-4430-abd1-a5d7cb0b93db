package com.ai.cutover.common.constant;

import lombok.experimental.UtilityClass;

/**
 * 通用常量类 定义系统中使用的各种通用常量
 *
 * <AUTHOR>
 */
@UtilityClass
public class CommonConstants {

	/**
	 * 通用字符串常量
	 */
	@UtilityClass
	public static class StringConstants {

		public static final String EMPTY = "";

		public static final String SPACE = " ";

		public static final String COMMA = ",";

		public static final String DOT = ".";

		public static final String COLON = ":";

		public static final String SEMICOLON = ";";

		public static final String DASH = "-";

		public static final String UNDERSCORE = "_";

		public static final String SLASH = "/";

		public static final String BACKSLASH = "\\";

	}

	/**
	 * 数字常量
	 */
	@UtilityClass
	public static class NumberConstants {

		public static final int ZERO = 0;

		public static final String ZERO_STR = "0";

		public static final int ONE = 1;

		public static final int MINUS_ONE = -1;

		public static final int DEFAULT_PAGE_SIZE = 20;

		public static final int MAX_PAGE_SIZE = 1000;

	}

	/**
	 * 布尔常量
	 */
	@UtilityClass
	public static class BooleanConstants {

		public static final String TRUE = "true";

		public static final String FALSE = "false";

		public static final String YES = "yes";

		public static final String NO = "no";

		public static final String Y = "Y";

		public static final String N = "N";

	}

	/**
	 * 日期时间格式常量
	 */
	@UtilityClass
	public static class DateTimeFormat {

		public static final String YYYY_MM_DD = "yyyy-MM-dd";

		public static final String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

		public static final String YYYYMMDD = "yyyyMMdd";

		public static final String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

		public static final String HH_MM_SS = "HH:mm:ss";

	}

	/**
	 * HTTP相关常量
	 */
	@UtilityClass
	public static class HttpConstants {

		public static final String GET = "GET";

		public static final String POST = "POST";

		public static final String PUT = "PUT";

		public static final String DELETE = "DELETE";

		public static final String PATCH = "PATCH";

		public static final String HEAD = "HEAD";

		public static final String OPTIONS = "OPTIONS";

		public static final String CONTENT_TYPE = "Content-Type";

		public static final String APPLICATION_JSON = "application/json";

		public static final String APPLICATION_XML = "application/xml";

		public static final String TEXT_PLAIN = "text/plain";

		public static final String TEXT_HTML = "text/html";

	}

	/**
	 * 编码常量
	 */
	@UtilityClass
	public static class EncodingConstants {

		public static final String UTF_8 = "UTF-8";

		public static final String GBK = "GBK";

		public static final String ISO_8859_1 = "ISO-8859-1";

	}

	/**
	 * 状态常量
	 */
	@UtilityClass
	public static class StatusConstants {

		public static final String ACTIVE = "ACTIVE";

		public static final String INACTIVE = "INACTIVE";

		public static final String ENABLED = "ENABLED";

		public static final String DISABLED = "DISABLED";

		public static final String SUCCESS = "SUCCESS";

		public static final String FAILURE = "FAILURE";

		public static final String PENDING = "PENDING";

		public static final String COMPLETED = "COMPLETED";

		public static final String CANCELLED = "CANCELLED";

	}

	/**
	 * 系统常量
	 */
	@UtilityClass
	public static class SystemConstants {

		public static final String SYSTEM_USER = "system";

		public static final String ADMIN_USER = "admin";

		public static final String ANONYMOUS_USER = "anonymous";

		public static final String DEFAULT_LOCALE = "zh_CN";

		public static final String DEFAULT_TIMEZONE = "Asia/Shanghai";

	}

	/**
	 * 工单状态
	 */
	@UtilityClass
	public static class OrderStatus {

		public static final String PENDING_REVIEW = "PENDING_REVIEW";

		public static final String REVIEWING = "REVIEWING";

		public static final String PENDING_EXECUTION = "PENDING_EXECUTION";

		public static final String EXECUTING = "EXECUTING";

		public static final String COMPLETED = "COMPLETED";

		public static final String REJECTED = "REJECTED";

	}

	/**
	 * 网元状态
	 */
	@UtilityClass
	public static class NetworkElementStatus {

		public static final String NORMAL = "NORMAL";

		public static final String CUTTING = "CUTTING";

		public static final String FAULT = "FAULT";

	}

	/**
	 * 网元类型
	 */
	@UtilityClass
	public static class NetworkElementType {

		public static final String BASE_STATION = "BASE_STATION";

		public static final String MACHINE_ROOM = "MACHINE_ROOM";

		public static final String LINE = "LINE";

	}

	/**
	 * 审批结果
	 */
	@UtilityClass
	public static class ApprovalResult {

		public static final String APPROVED = "APPROVED";

		public static final String REJECTED = "REJECTED";

	}

	/**
	 * 地市编码
	 */
	@UtilityClass
	public static class CityCode {

		public static final String XIAN = "XIAN";

		public static final String BAOJI = "BAOJI";

		public static final String XIANYANG = "XIANYANG";

		public static final String WEINAN = "WEINAN";

		public static final String TONGCHUAN = "TONGCHUAN";

		public static final String YANAN = "YANAN";

		public static final String YULIN = "YULIN";

		public static final String HANZHONG = "HANZHONG";

		public static final String ANKANG = "ANKANG";

		public static final String SHANGLUO = "SHANGLUO";

	}

	/**
	 * 分页常量
	 */
	@UtilityClass
	public static class PageConstants {

		public static final int DEFAULT_PAGE_NUM = 1;

		public static final int DEFAULT_PAGE_SIZE = 10;

		public static final int MAX_PAGE_SIZE = 100;

	}

	/**
	 * 割接专业类型
	 */
	@UtilityClass
	public static class CutoverProfessionType {

		public static final String TRANSMISSION = "TRANSMISSION";

		public static final String WIRELESS = "WIRELESS";

		public static final String ACCESS = "ACCESS";

	}

	/**
	 * SQL相关常量
	 */
	@UtilityClass
	public static class SQLConstants {

		public static final String LIMIT_1 = " LIMIT 1";

	}

	/**
	 * 临时资源相关常量
	 */
	@UtilityClass
	public static class TempConstants {

		/**
		 * 临时部署名称前缀
		 */
		public static final String TEMP_DEPLOYMENT_PREFIX = "temp-validation-";

		/**
		 * 临时验证文件名
		 */
		public static final String TEMP_VALIDATION_FILE_NAME = "validation.bpmn";

	}

	/**
	 * 格式化模板相关常量
	 */
	@UtilityClass
	public static class FormatConstants {

		/**
		 * 时间格式化模板
		 */
		public static final String TIME_FORMAT_DAYS_HOURS_MINUTES = "{}天{}小时{}分钟";

		public static final String TIME_FORMAT_HOURS_MINUTES = "{}小时{}分钟";

		public static final String TIME_FORMAT_MINUTES_SECONDS = "{}分钟{}秒";

		public static final String TIME_FORMAT_SECONDS = "{}秒";

	}

	/**
	 * 默认值相关常量
	 */
	@UtilityClass
	public static class DefaultConstants {

		/**
		 * 流程默认名称
		 */
		public static final String PROCESS_DEFAULT_NAME = "未命名流程";

	}

}
