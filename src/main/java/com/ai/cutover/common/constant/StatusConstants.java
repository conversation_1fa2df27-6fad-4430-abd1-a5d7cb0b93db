package com.ai.cutover.common.constant;

import lombok.experimental.UtilityClass;

/**
 * 状态常量类 统一管理各种状态相关的常量
 *
 * <AUTHOR>
 */
@UtilityClass
public class StatusConstants {

	/**
	 * 通用状态
	 */
	@UtilityClass
	public static class Common {

		/** 正常 */
		public static final String NORMAL = "NORMAL";

		/** 禁用 */
		public static final String DISABLED = "DISABLED";

		/** 删除 */
		public static final String DELETED = "DELETED";

	}

	/**
	 * 用户状态
	 */
	@UtilityClass
	public static class User {

		/** 正常 */
		public static final String NORMAL = "NORMAL";

		/** 禁用 */
		public static final String DISABLED = "DISABLED";

		/** 锁定 */
		public static final String LOCKED = "LOCKED";

		/** 过期 */
		public static final String EXPIRED = "EXPIRED";

	}

	/**
	 * 角色状态
	 */
	@UtilityClass
	public static class Role {

		/** 正常 */
		public static final String NORMAL = "NORMAL";

		/** 禁用 */
		public static final String DISABLED = "DISABLED";

	}

	/**
	 * 部门状态
	 */
	@UtilityClass
	public static class Dept {

		/** 正常 */
		public static final String NORMAL = "NORMAL";

		/** 禁用 */
		public static final String DISABLED = "DISABLED";

	}

	/**
	 * 菜单状态
	 */
	@UtilityClass
	public static class Menu {

		/** 正常 */
		public static final String NORMAL = "NORMAL";

		/** 禁用 */
		public static final String DISABLED = "DISABLED";

	}

	/**
	 * 权限状态
	 */
	@UtilityClass
	public static class Permission {

		/** 正常 */
		public static final String NORMAL = "NORMAL";

		/** 禁用 */
		public static final String DISABLED = "DISABLED";

	}

	/**
	 * 流程状态
	 */
	@UtilityClass
	public static class Process {

		/** 草稿 */
		public static final String DRAFT = "DRAFT";

		/** 运行中 */
		public static final String RUNNING = "RUNNING";

		/** 已完成 */
		public static final String COMPLETED = "COMPLETED";

		/** 已终止 */
		public static final String TERMINATED = "TERMINATED";

		/** 已暂停 */
		public static final String SUSPENDED = "SUSPENDED";

	}

	/**
	 * 任务状态
	 */
	@UtilityClass
	public static class Task {

		/** 待处理 */
		public static final String PENDING = "PENDING";

		/** 处理中 */
		public static final String IN_PROGRESS = "IN_PROGRESS";

		/** 已完成 */
		public static final String COMPLETED = "COMPLETED";

		/** 已拒绝 */
		public static final String REJECTED = "REJECTED";

	}

	/**
	 * 切换状态
	 */
	@UtilityClass
	public static class Cutover {

		/** 计划中 */
		public static final String PLANNED = "PLANNED";

		/** 准备中 */
		public static final String PREPARING = "PREPARING";

		/** 执行中 */
		public static final String EXECUTING = "EXECUTING";

		/** 已完成 */
		public static final String COMPLETED = "COMPLETED";

		/** 已回滚 */
		public static final String ROLLBACK = "ROLLBACK";

		/** 失败 */
		public static final String FAILED = "FAILED";

	}

}
