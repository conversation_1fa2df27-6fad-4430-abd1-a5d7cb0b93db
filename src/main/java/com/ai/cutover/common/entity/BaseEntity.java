package com.ai.cutover.common.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 实体基类 包含所有实体的公共字段：主键、创建时间、更新时间
 *
 * <AUTHOR>
 */
@Data
public abstract class BaseEntity {

	/**
	 * 主键ID（使用雪花算法生成）
	 */
	@Id(keyType = KeyType.Generator, value = KeyGenerators.snowFlakeId)
	private Long id;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建人ID
	 */
	private Long createBy;

	/**
	 * 更新人ID
	 */
	private Long updateBy;

}
