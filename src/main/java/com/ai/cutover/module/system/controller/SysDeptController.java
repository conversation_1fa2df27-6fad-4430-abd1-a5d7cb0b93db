package com.ai.cutover.module.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ai.cutover.module.system.constant.PermissionConstants;
import com.ai.cutover.module.system.model.dto.SysDeptDTO;
import com.ai.cutover.module.system.model.resp.SysDeptResp;
import com.ai.cutover.module.system.model.req.CreateDeptReq;
import com.ai.cutover.module.system.model.req.UpdateDeptReq;
import com.ai.cutover.module.system.model.req.DeptQueryReq;
import com.ai.cutover.module.system.service.SysDeptService;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 系统部门控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/system/dept")
@RequiredArgsConstructor
@Validated
public class SysDeptController {

	private final SysDeptService sysDeptService;

	private final Converter converter;

	/**
	 * 创建部门
	 */
	@PostMapping("/create")
	@SaCheckPermission(PermissionConstants.Dept.CREATE)
	public SysDeptResp createDept(@Valid @RequestBody CreateDeptReq request) {
		SysDeptDTO deptDTO = sysDeptService.createDept(request);
		return converter.convert(deptDTO, SysDeptResp.class);
	}

	/**
	 * 更新部门
	 */
	@PostMapping("/update")
	@SaCheckPermission(PermissionConstants.Dept.UPDATE)
	public SysDeptResp updateDept(@Valid @RequestBody UpdateDeptReq request) {
		SysDeptDTO deptDTO = sysDeptService.updateDept(request);
		return converter.convert(deptDTO, SysDeptResp.class);
	}

	/**
	 * 删除部门
	 */
	@PostMapping("/delete")
	@SaCheckPermission(PermissionConstants.Dept.DELETE)
	public void deleteDept(@RequestParam @NotNull Long deptId) {
		sysDeptService.deleteDept(deptId);
	}

	/**
	 * 根据ID查询部门详情
	 */
	@GetMapping("/detail")
	@SaCheckPermission(PermissionConstants.Dept.VIEW)
	public SysDeptResp getDeptDetail(@RequestParam @NotNull Long id) {
		SysDeptDTO deptDTO = sysDeptService.getDeptById(id);
		return converter.convert(deptDTO, SysDeptResp.class);
	}

	/**
	 * 根据部门编码查询部门
	 */
	@GetMapping("/by-code")
	@SaCheckPermission(PermissionConstants.Dept.VIEW)
	public SysDeptResp getDeptByCode(@RequestParam String deptCode) {
		SysDeptDTO deptDTO = sysDeptService.getDeptByCode(deptCode);
		return converter.convert(deptDTO, SysDeptResp.class);
	}

	/**
	 * 查询部门树形列表
	 */
	@GetMapping("/tree")
	@SaCheckPermission(PermissionConstants.Dept.VIEW)
	public List<SysDeptResp> getDeptTree(DeptQueryReq request) {
		List<SysDeptDTO> deptDTOList = sysDeptService.getDeptTree(request);
		return converter.convert(deptDTOList, SysDeptResp.class);
	}

	/**
	 * 根据父部门ID查询子部门列表
	 */
	@GetMapping("/children")
	public List<SysDeptResp> getDeptsByParentId(@RequestParam @NotNull Long parentId) {
		List<SysDeptDTO> deptDTOList = sysDeptService.getDeptsByParentId(parentId);
		return converter.convert(deptDTOList, SysDeptResp.class);
	}

	/**
	 * 查询部门的所有子部门ID
	 */
	@GetMapping("/children-ids")
	public List<Long> getChildrenIds(@RequestParam @NotNull Long deptId) {
		return sysDeptService.getChildrenIds(deptId);
	}

	/**
	 * 查询部门的所有父部门ID
	 */
	@GetMapping("/parent-ids")
	public List<Long> getParentIds(@RequestParam @NotNull Long deptId) {
		return sysDeptService.getParentIds(deptId);
	}

	/**
	 * 更新部门状态
	 */
	@PostMapping("/update-status")
	public void updateDeptStatus(@RequestParam @NotNull Long deptId, @RequestParam String status) {
		sysDeptService.updateDeptStatus(deptId, status);
	}

	/**
	 * 检查部门编码是否存在
	 */
	@GetMapping("/exists-code")
	public Boolean existsByDeptCode(@RequestParam String deptCode, @RequestParam(required = false) Long excludeId) {
		return sysDeptService.existsByDeptCode(deptCode, excludeId);
	}

	/**
	 * 检查部门名称是否存在（同级）
	 */
	@GetMapping("/exists-name")
	public Boolean existsByDeptName(@RequestParam String deptName, @RequestParam @NotNull Long parentId,
			@RequestParam(required = false) Long excludeId) {
		return sysDeptService.existsByDeptName(deptName, parentId, excludeId);
	}

	/**
	 * 检查部门是否有子部门
	 */
	@GetMapping("/has-children")
	public Boolean hasChildren(@RequestParam @NotNull Long deptId) {
		return sysDeptService.hasChildren(deptId);
	}

	/**
	 * 统计部门下的用户数量
	 */
	@GetMapping("/user-count")
	public Integer countUsersByDeptId(@RequestParam @NotNull Long deptId) {
		return sysDeptService.countUsersByDeptId(deptId);
	}

}
