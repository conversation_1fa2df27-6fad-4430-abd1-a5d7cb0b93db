package com.ai.cutover.module.system.model.req;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 重置密码请求
 *
 * <AUTHOR>
 */
@Data
public class ResetPasswordReq {

	/**
	 * 用户ID
	 */
	@NotNull(message = "用户ID不能为空")
	private Long userId;

	/**
	 * 新密码
	 */
	@NotBlank(message = "新密码不能为空")
	@Size(min = 6, max = 20, message = "新密码长度必须在6-20个字符之间")
	private String newPassword;

}
