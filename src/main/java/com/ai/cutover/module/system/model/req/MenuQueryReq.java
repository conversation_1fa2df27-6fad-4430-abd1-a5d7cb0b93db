package com.ai.cutover.module.system.model.req;

import lombok.Data;

/**
 * 菜单查询请求类
 *
 * <AUTHOR>
 */
@Data
public class MenuQueryReq {

	/**
	 * 菜单名称（模糊查询）
	 */
	private String menuName;

	/**
	 * 菜单类型（DIRECTORY-目录，MENU-菜单，BUTTON-按钮）
	 */
	private String menuType;

	/**
	 * 菜单状态（NORMAL-正常，DISABLED-禁用）
	 */
	private String status;

	/**
	 * 是否显示（Y-是，N-否）
	 */
	private String visible;

	/**
	 * 权限标识（模糊查询）
	 */
	private String permission;

}
