package com.ai.cutover.module.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ai.cutover.module.system.constant.PermissionConstants;
import com.ai.cutover.module.system.model.dto.SysMenuDTO;
import com.ai.cutover.module.system.model.req.CreateMenuReq;
import com.ai.cutover.module.system.model.req.UpdateMenuReq;
import com.ai.cutover.module.system.model.req.MenuQueryReq;
import com.ai.cutover.module.system.service.SysMenuService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 系统菜单控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/system/menu")
@RequiredArgsConstructor
@Validated
public class SysMenuController {

	private final SysMenuService sysMenuService;

	/**
	 * 创建菜单
	 */
	@PostMapping("/create")
	@SaCheckPermission(PermissionConstants.Menu.CREATE)
	public SysMenuDTO createMenu(@Valid @RequestBody CreateMenuReq request) {
		return sysMenuService.createMenu(request);
	}

	/**
	 * 更新菜单
	 */
	@PostMapping("/update")
	@SaCheckPermission(PermissionConstants.Menu.UPDATE)
	public SysMenuDTO updateMenu(@Valid @RequestBody UpdateMenuReq request) {
		return sysMenuService.updateMenu(request);
	}

	/**
	 * 删除菜单
	 */
	@PostMapping("/delete")
	@SaCheckPermission(PermissionConstants.Menu.DELETE)
	public void deleteMenu(@RequestParam @NotNull Long menuId) {
		sysMenuService.deleteMenu(menuId);
	}

	/**
	 * 根据ID查询菜单详情
	 */
	@GetMapping("/detail")
	@SaCheckPermission(PermissionConstants.Menu.VIEW)
	public SysMenuDTO getMenuDetail(@RequestParam @NotNull Long id) {
		return sysMenuService.getMenuById(id);
	}

	/**
	 * 查询菜单树形列表
	 */
	@GetMapping("/tree")
	@SaCheckPermission(PermissionConstants.Menu.VIEW)
	public List<SysMenuDTO> getMenuTree(MenuQueryReq request) {
		return sysMenuService.getMenuTree(request);
	}

	/**
	 * 根据父菜单ID查询子菜单列表
	 */
	@GetMapping("/children")
	@SaCheckPermission(PermissionConstants.Menu.VIEW)
	public List<SysMenuDTO> getMenusByParentId(@RequestParam @NotNull Long parentId) {
		return sysMenuService.getMenusByParentId(parentId);
	}

	/**
	 * 根据用户ID查询菜单列表
	 */
	@GetMapping("/by-user")
	public List<SysMenuDTO> getMenusByUserId(@RequestParam @NotNull Long userId) {
		return sysMenuService.getMenusByUserId(userId);
	}

	/**
	 * 根据角色ID查询菜单列表
	 */
	@GetMapping("/by-role")
	public List<SysMenuDTO> getMenusByRoleId(@RequestParam @NotNull Long roleId) {
		return sysMenuService.getMenusByRoleId(roleId);
	}

	/**
	 * 查询所有正常状态的菜单
	 */
	@GetMapping("/all")
	public List<SysMenuDTO> getAllMenus() {
		return sysMenuService.getAllMenus();
	}

	/**
	 * 更新菜单状态
	 */
	@PostMapping("/update-status")
	public void updateMenuStatus(@RequestParam @NotNull Long menuId, @RequestParam String status) {
		sysMenuService.updateMenuStatus(menuId, status);
	}

	/**
	 * 检查菜单名称是否存在（同级）
	 */
	@GetMapping("/exists-name")
	public Boolean existsByMenuName(@RequestParam String menuName, @RequestParam @NotNull Long parentId,
			@RequestParam(required = false) Long excludeId) {
		return sysMenuService.existsByMenuName(menuName, parentId, excludeId);
	}

	/**
	 * 检查权限标识是否存在
	 */
	@GetMapping("/exists-permission")
	public Boolean existsByPermission(@RequestParam String permission, @RequestParam(required = false) Long excludeId) {
		return sysMenuService.existsByPermission(permission, excludeId);
	}

	/**
	 * 检查菜单是否有子菜单
	 */
	@GetMapping("/has-children")
	public Boolean hasChildren(@RequestParam @NotNull Long menuId) {
		return sysMenuService.hasChildren(menuId);
	}

}
