package com.ai.cutover.module.system.model.entity;

import com.ai.cutover.common.entity.BaseEntity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 系统菜单实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("sys_menu")
public class SysMenu extends BaseEntity {

	/**
	 * 父菜单ID（0表示根菜单）
	 */
	@Column("parent_id")
	private Long parentId;

	/**
	 * 菜单名称
	 */
	@Column("menu_name")
	private String menuName;

	/**
	 * 菜单标题
	 */
	@Column("title")
	private String title;

	/**
	 * 菜单类型（DIRECTORY-目录，MENU-菜单，BUTTON-按钮）
	 */
	@Column("menu_type")
	private String menuType;

	/**
	 * 路由地址
	 */
	@Column("path")
	private String path;

	/**
	 * 组件路径
	 */
	@Column("component")
	private String component;

	/**
	 * 权限标识
	 */
	@Column("permission")
	private String permission;

	/**
	 * 菜单图标
	 */
	@Column("icon")
	private String icon;

	/**
	 * 显示顺序
	 */
	@Column("sort_order")
	private Integer sortOrder;

	/**
	 * 是否外链（Y-是，N-否）
	 */
	@Column("is_frame")
	private String isFrame;

	/**
	 * 是否缓存（Y-是，N-否）
	 */
	@Column("is_cache")
	private String isCache;

	/**
	 * 是否显示（Y-是，N-否）
	 */
	@Column("visible")
	private String visible;

	/**
	 * 菜单状态（NORMAL-正常，DISABLED-禁用）
	 */
	@Column("status")
	private String status;

	/**
	 * 备注
	 */
	@Column("remark")
	private String remark;

}
