package com.ai.cutover.module.system.model.req;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 更新部门请求
 *
 * <AUTHOR>
 */
@Data
public class UpdateDeptReq {

	/**
	 * 部门ID
	 */
	@NotNull(message = "部门ID不能为空")
	private Long id;

	/**
	 * 父部门ID
	 */
	@NotNull(message = "父部门ID不能为空")
	private Long parentId;

	/**
	 * 部门编码
	 */
	@NotBlank(message = "部门编码不能为空")
	@Size(max = 50, message = "部门编码长度不能超过50个字符")
	private String deptCode;

	/**
	 * 部门名称
	 */
	@NotBlank(message = "部门名称不能为空")
	@Size(max = 100, message = "部门名称长度不能超过100个字符")
	private String deptName;

	/**
	 * 部门类型
	 */
	private String deptType;

	/**
	 * 显示顺序
	 */
	private Integer sortOrder;

	/**
	 * 负责人ID
	 */
	private Long leaderId;

	/**
	 * 联系电话
	 */
	@Size(max = 20, message = "联系电话长度不能超过20个字符")
	private String phone;

	/**
	 * 邮箱
	 */
	@Size(max = 100, message = "邮箱长度不能超过100个字符")
	private String email;

	/**
	 * 部门地址
	 */
	@Size(max = 200, message = "部门地址长度不能超过200个字符")
	private String address;

	/**
	 * 部门状态
	 */
	private String status;

	/**
	 * 备注
	 */
	@Size(max = 500, message = "备注长度不能超过500个字符")
	private String remark;

}
