package com.ai.cutover.module.system.model.resp;

import lombok.Builder;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.Set;

/**
 * 登录响应
 *
 * <AUTHOR>
 */
@Data
@Builder
public class LoginResp {

	/**
	 * 访问令牌
	 */
	private String accessToken;

	/**
	 * 令牌类型
	 */
	@Builder.Default
	private String tokenType = "Bearer";

	/**
	 * 过期时间（秒）
	 */
	private Long expiresIn;

	/**
	 * 用户ID
	 */
	private Long userId;

	/**
	 * 用户名
	 */
	private String username;

	/**
	 * 真实姓名
	 */
	private String realName;

	/**
	 * 头像
	 */
	private String avatar;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 手机号
	 */
	private String phone;

	/**
	 * 部门ID
	 */
	private Long deptId;

	/**
	 * 部门名称
	 */
	private String deptName;

	/**
	 * 角色列表
	 */
	private Set<String> roles;

	/**
	 * 权限列表
	 */
	private Set<String> permissions;

	/**
	 * 最后登录时间
	 */
	private LocalDateTime lastLoginTime;

	/**
	 * 最后登录IP
	 */
	private String lastLoginIp;

}
