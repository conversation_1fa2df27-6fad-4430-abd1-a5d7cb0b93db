package com.ai.cutover.module.system.model.resp;

import com.ai.cutover.module.system.model.dto.SysUserDTO;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统用户响应类
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysUserDTO.class, reverseConvertGenerate = false)
public class SysUserResp {

	/**
	 * 用户ID
	 */
	private Long id;

	/**
	 * 用户名
	 */
	private String username;

	/**
	 * 昵称
	 */
	private String nickname;

	/**
	 * 真实姓名
	 */
	private String realName;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 手机号
	 */
	private String phone;

	/**
	 * 性别（MALE-男，FEMALE-女，UNKNOWN-未知）
	 */
	private String gender;

	/**
	 * 头像
	 */
	private String avatar;

	/**
	 * 部门ID
	 */
	private Long deptId;

	/**
	 * 职位
	 */
	private String position;

	/**
	 * 用户状态（NORMAL-正常，DISABLED-禁用，LOCKED-锁定，EXPIRED-过期）
	 */
	private String status;

	/**
	 * 最后登录时间
	 */
	private LocalDateTime lastLoginTime;

	/**
	 * 最后登录IP
	 */
	private String lastLoginIp;

	/**
	 * 密码过期时间
	 */
	private LocalDateTime passwordExpireTime;

	/**
	 * 账户过期时间
	 */
	private LocalDateTime accountExpireTime;

	/**
	 * 是否内置用户（Y-是，N-否）
	 */
	private String builtinFlag;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建人
	 */
	private Long createBy;

	/**
	 * 更新人
	 */
	private Long updateBy;

	// 扩展字段

	/**
	 * 部门名称
	 */
	private String deptName;

	/**
	 * 角色ID列表
	 */
	private List<Long> roleIds;

	/**
	 * 角色名称列表
	 */
	private List<String> roleNames;

	/**
	 * 权限标识列表
	 */
	private List<String> permissions;

}
