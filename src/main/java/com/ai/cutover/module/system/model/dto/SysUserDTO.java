package com.ai.cutover.module.system.model.dto;

import com.ai.cutover.module.system.model.entity.SysUser;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 系统用户DTO
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysUser.class)
public class SysUserDTO {

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 用户名
	 */
	private String username;

	/**
	 * 真实姓名
	 */
	private String realName;

	/**
	 * 昵称
	 */
	private String nickname;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 手机号
	 */
	private String phone;

	/**
	 * 性别
	 */
	private String gender;

	/**
	 * 头像地址
	 */
	private String avatar;

	/**
	 * 部门ID
	 */
	private Long deptId;

	/**
	 * 部门名称
	 */
	private String deptName;

	/**
	 * 职位
	 */
	private String position;

	/**
	 * 用户状态
	 */
	private String status;

	/**
	 * 是否为内置用户
	 */
	private String builtinFlag;

	/**
	 * 最后登录时间
	 */
	private LocalDateTime lastLoginTime;

	/**
	 * 最后登录IP
	 */
	private String lastLoginIp;

	/**
	 * 登录失败次数
	 */
	private Integer loginFailCount;

	/**
	 * 账户锁定时间
	 */
	private LocalDateTime lockTime;

	/**
	 * 密码过期时间
	 */
	private LocalDateTime passwordExpireTime;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 角色ID列表
	 */
	private List<Long> roleIds;

	/**
	 * 角色名称列表
	 */
	private List<String> roleNames;

	/**
	 * 权限标识列表
	 */
	private Set<String> permissions;

}
