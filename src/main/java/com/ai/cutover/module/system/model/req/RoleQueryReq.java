package com.ai.cutover.module.system.model.req;

import lombok.Data;

/**
 * 角色查询请求
 *
 * <AUTHOR>
 */
@Data
public class RoleQueryReq {

	/**
	 * 页码
	 */
	private Integer pageNum = 1;

	/**
	 * 页大小
	 */
	private Integer pageSize = 10;

	/**
	 * 角色编码（模糊查询）
	 */
	private String roleCode;

	/**
	 * 角色名称（模糊查询）
	 */
	private String roleName;

	/**
	 * 角色状态
	 */
	private String status;

	/**
	 * 开始时间
	 */
	private String startTime;

	/**
	 * 结束时间
	 */
	private String endTime;

}
