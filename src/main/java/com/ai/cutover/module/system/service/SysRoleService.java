package com.ai.cutover.module.system.service;

import com.ai.cutover.module.system.model.dto.SysRoleDTO;
import com.ai.cutover.module.system.model.req.CreateRoleReq;
import com.ai.cutover.module.system.model.req.UpdateRoleReq;
import com.ai.cutover.module.system.model.req.RoleQueryReq;
import com.mybatisflex.core.paginate.Page;

import java.util.List;

/**
 * 系统角色服务接口
 *
 * <AUTHOR>
 */
public interface SysRoleService {

	/**
	 * 创建角色
	 * @param request 创建角色请求
	 * @return 角色DTO
	 */
	SysRoleDTO createRole(CreateRoleReq request);

	/**
	 * 更新角色
	 * @param request 更新角色请求
	 * @return 角色DTO
	 */
	SysRoleDTO updateRole(UpdateRoleReq request);

	/**
	 * 删除角色
	 * @param roleId 角色ID
	 */
	void deleteRole(Long roleId);

	/**
	 * 批量删除角色
	 * @param roleIds 角色ID列表
	 */
	void batchDeleteRoles(List<Long> roleIds);

	/**
	 * 根据ID查询角色
	 * @param roleId 角色ID
	 * @return 角色DTO
	 */
	SysRoleDTO getRoleById(Long roleId);

	/**
	 * 根据角色编码查询角色
	 * @param roleCode 角色编码
	 * @return 角色DTO
	 */
	SysRoleDTO getRoleByCode(String roleCode);

	/**
	 * 分页查询角色列表
	 * @param request 查询请求
	 * @return 角色分页列表
	 */
	Page<SysRoleDTO> getRolePage(RoleQueryReq request);

	/**
	 * 查询所有角色列表
	 * @return 角色列表
	 */
	List<SysRoleDTO> getAllRoles();

	/**
	 * 根据用户ID查询角色列表
	 * @param userId 用户ID
	 * @return 角色列表
	 */
	List<SysRoleDTO> getRolesByUserId(Long userId);

	/**
	 * 更新角色状态
	 * @param roleId 角色ID
	 * @param status 状态
	 */
	void updateRoleStatus(Long roleId, String status);

	/**
	 * 分配角色权限
	 * @param roleId 角色ID
	 * @param permissionIds 权限ID列表
	 */
	void assignRolePermissions(Long roleId, List<Long> permissionIds);

	/**
	 * 检查角色编码是否存在
	 * @param roleCode 角色编码
	 * @param excludeId 排除的角色ID
	 * @return 是否存在
	 */
	boolean existsByRoleCode(String roleCode, Long excludeId);

	/**
	 * 检查角色名称是否存在
	 * @param roleName 角色名称
	 * @param excludeId 排除的角色ID
	 * @return 是否存在
	 */
	boolean existsByRoleName(String roleName, Long excludeId);

	/**
	 * 统计角色下的用户数量
	 * @param roleId 角色ID
	 * @return 用户数量
	 */
	Integer countUsersByRoleId(Long roleId);

}
