package com.ai.cutover.module.system.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 用户状态枚举
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum UserStatus {

	/**
	 * 正常
	 */
	NORMAL("NORMAL", "正常"),

	/**
	 * 禁用
	 */
	DISABLED("DISABLED", "禁用"),

	/**
	 * 锁定
	 */
	LOCKED("LOCKED", "锁定"),

	/**
	 * 过期
	 */
	EXPIRED("EXPIRED", "过期");

	/**
	 * 状态码
	 */
	private final String code;

	/**
	 * 状态描述
	 */
	private final String description;

	/**
	 * 根据状态码获取枚举
	 * @param code 状态码
	 * @return 用户状态枚举
	 */
	public static UserStatus fromCode(String code) {
		for (UserStatus status : values()) {
			if (status.getCode().equals(code)) {
				return status;
			}
		}
		throw new IllegalArgumentException("未知的用户状态码: " + code);
	}

	/**
	 * 检查状态码是否有效
	 * @param code 状态码
	 * @return 是否有效
	 */
	public static boolean isValid(String code) {
		for (UserStatus status : values()) {
			if (status.getCode().equals(code)) {
				return true;
			}
		}
		return false;
	}

}
