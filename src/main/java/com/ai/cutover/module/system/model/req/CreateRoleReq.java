package com.ai.cutover.module.system.model.req;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.util.List;

/**
 * 创建角色请求
 *
 * <AUTHOR>
 */
@Data
public class CreateRoleReq {

	/**
	 * 角色编码
	 */
	@NotBlank(message = "角色编码不能为空")
	@Size(max = 50, message = "角色编码长度不能超过50个字符")
	private String roleCode;

	/**
	 * 角色名称
	 */
	@NotBlank(message = "角色名称不能为空")
	@Size(max = 100, message = "角色名称长度不能超过100个字符")
	private String roleName;

	/**
	 * 角色描述
	 */
	@Size(max = 500, message = "角色描述长度不能超过500个字符")
	private String description;

	/**
	 * 显示顺序
	 */
	private Integer sortOrder;

	/**
	 * 角色状态
	 */
	private String status;

	/**
	 * 数据权限范围
	 */
	private String dataScope;

	/**
	 * 权限ID列表
	 */
	private List<Long> permissionIds;

	/**
	 * 备注
	 */
	@Size(max = 500, message = "备注长度不能超过500个字符")
	private String remark;

}
