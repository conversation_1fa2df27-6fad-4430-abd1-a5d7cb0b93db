package com.ai.cutover.module.system.model.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 验证码验证响应类
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CaptchaVerifyResp {

	/**
	 * 验证是否通过
	 */
	private Boolean valid;

	/**
	 * 验证消息
	 */
	private String message;

	/**
	 * 剩余尝试次数
	 */
	private Integer remainingAttempts;

	/**
	 * 便捷构造方法 - 只返回验证结果
	 * @param valid 是否验证通过
	 * @return CaptchaVerifyResp
	 */
	public static CaptchaVerifyResp of(Boolean valid) {
		return CaptchaVerifyResp.builder().valid(valid).message(valid ? "验证通过" : "验证失败").build();
	}

	/**
	 * 便捷构造方法 - 返回验证结果和消息
	 * @param valid 是否验证通过
	 * @param message 验证消息
	 * @return CaptchaVerifyResp
	 */
	public static CaptchaVerifyResp of(Boolean valid, String message) {
		return CaptchaVerifyResp.builder().valid(valid).message(message).build();
	}

}
