package com.ai.cutover.module.system.model.dto;

import com.ai.cutover.module.system.model.entity.SysRole;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统角色DTO
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysRole.class)
public class SysRoleDTO {

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 角色编码
	 */
	private String roleCode;

	/**
	 * 角色名称
	 */
	private String roleName;

	/**
	 * 角色描述
	 */
	private String description;

	/**
	 * 显示顺序
	 */
	private Integer sortOrder;

	/**
	 * 角色状态
	 */
	private String status;

	/**
	 * 数据权限范围
	 */
	private String dataScope;

	/**
	 * 是否为内置角色
	 */
	private String builtinFlag;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 权限ID列表
	 */
	private List<Long> permissionIds;

	/**
	 * 菜单ID列表
	 */
	private List<Long> menuIds;

	/**
	 * 用户数量
	 */
	private Integer userCount;

}
