package com.ai.cutover.module.system.model.entity;

import com.ai.cutover.common.entity.BaseEntity;
import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 系统用户实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Table("sys_user")
public class SysUser extends BaseEntity {

	/**
	 * 用户名
	 */
	@Column("username")
	private String username;

	/**
	 * 密码
	 */
	@Column("password")
	private String password;

	/**
	 * 真实姓名
	 */
	@Column("real_name")
	private String realName;

	/**
	 * 昵称
	 */
	@Column("nickname")
	private String nickname;

	/**
	 * 邮箱
	 */
	@Column("email")
	private String email;

	/**
	 * 手机号
	 */
	@Column("phone")
	private String phone;

	/**
	 * 性别（M-男，F-女，U-未知）
	 */
	@Column("gender")
	private String gender;

	/**
	 * 头像URL
	 */
	@Column("avatar")
	private String avatar;

	/**
	 * 用户状态（NORMAL-正常，DISABLED-禁用，LOCKED-锁定）
	 */
	@Column("status")
	private String status;

	/**
	 * 部门ID
	 */
	@Column("dept_id")
	private Long deptId;

	/**
	 * 职位
	 */
	@Column("position")
	private String position;

	/**
	 * 最后登录时间
	 */
	@Column("last_login_time")
	private LocalDateTime lastLoginTime;

	/**
	 * 最后登录IP
	 */
	@Column("last_login_ip")
	private String lastLoginIp;

	/**
	 * 锁定时间
	 */
	@Column("lock_time")
	private LocalDateTime lockTime;

	/**
	 * 密码过期时间
	 */
	@Column("password_expire_time")
	private LocalDateTime passwordExpireTime;

	/**
	 * 是否内置用户（Y-是，N-否）
	 */
	@Column("builtin_flag")
	private String builtinFlag;

	/**
	 * 备注
	 */
	@Column("remark")
	private String remark;

}
