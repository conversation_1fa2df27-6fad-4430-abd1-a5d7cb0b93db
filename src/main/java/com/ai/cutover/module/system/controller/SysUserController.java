package com.ai.cutover.module.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import cn.dev33.satoken.annotation.SaCheckRole;
import cn.dev33.satoken.stp.StpUtil;
import com.ai.cutover.module.system.constant.PermissionConstants;
import com.ai.cutover.module.system.constant.RoleConstants;
import com.ai.cutover.module.system.model.dto.SysUserDTO;
import com.ai.cutover.module.system.model.req.CreateUserReq;
import com.ai.cutover.module.system.model.req.UpdateUserReq;
import com.ai.cutover.module.system.model.req.UserQueryReq;
import com.ai.cutover.module.system.model.resp.SysUserResp;
import com.ai.cutover.module.system.service.SysUserService;
import com.mybatisflex.core.paginate.Page;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Set;

/**
 * 系统用户控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/system/user")
@RequiredArgsConstructor
@Validated
public class SysUserController {

	private final SysUserService sysUserService;

	private final Converter converter;

	/**
	 * 创建用户
	 */
	@PostMapping("/create")
	@SaCheckPermission(PermissionConstants.User.CREATE)
	public SysUserResp createUser(@Valid @RequestBody CreateUserReq request) {
		SysUserDTO userDTO = sysUserService.createUser(request);
		return converter.convert(userDTO, SysUserResp.class);
	}

	/**
	 * 更新用户
	 */
	@PostMapping("/update")
	@SaCheckPermission(PermissionConstants.User.UPDATE)
	public SysUserResp updateUser(@Valid @RequestBody UpdateUserReq request) {
		SysUserDTO userDTO = sysUserService.updateUser(request);
		return converter.convert(userDTO, SysUserResp.class);
	}

	/**
	 * 删除用户
	 */
	@PostMapping("/delete")
	@SaCheckPermission(PermissionConstants.User.DELETE)
	public void deleteUser(@RequestParam @NotNull Long userId) {
		sysUserService.deleteUser(userId);
	}

	/**
	 * 批量删除用户
	 */
	@PostMapping("/batch-delete")
	@SaCheckPermission(PermissionConstants.User.DELETE)
	public void batchDeleteUsers(@RequestBody @NotEmpty List<Long> userIds) {
		sysUserService.batchDeleteUsers(userIds);
	}

	/**
	 * 根据ID查询用户详情
	 */
	@GetMapping("/detail")
	@SaCheckPermission(PermissionConstants.User.VIEW)
	public SysUserResp getUserDetail(@RequestParam @NotNull Long id) {
		SysUserDTO userDTO = sysUserService.getUserById(id);
		return converter.convert(userDTO, SysUserResp.class);
	}

	/**
	 * 根据用户名查询用户
	 */
	@GetMapping("/by-username")
	@SaCheckPermission(PermissionConstants.User.VIEW)
	public SysUserResp getUserByUsername(@RequestParam String username) {
		SysUserDTO userDTO = sysUserService.getUserByUsername(username);
		return converter.convert(userDTO, SysUserResp.class);
	}

	/**
	 * 分页查询用户列表
	 */
	@GetMapping("/page")
	@SaCheckPermission(PermissionConstants.User.VIEW)
	public Page<SysUserResp> getUserPage(UserQueryReq request) {
		Page<SysUserDTO> userDTOPage = sysUserService.getUserPage(request);

		// 使用 MyBatis Flex 提供的分页转换 API
		return userDTOPage.map(sysUserDTO -> converter.convert(sysUserDTO, SysUserResp.class));
	}

	/**
	 * 查询所有用户列表
	 */
	@GetMapping("/list")
	public List<SysUserResp> getAllUsers() {
		List<SysUserDTO> userDTOList = sysUserService.getAllUsers();
		return converter.convert(userDTOList, SysUserResp.class);
	}

	/**
	 * 根据部门ID查询用户列表
	 */
	@GetMapping("/by-dept")
	public List<SysUserResp> getUsersByDeptId(@RequestParam @NotNull Long deptId) {
		List<SysUserDTO> userDTOList = sysUserService.getUsersByDeptId(deptId);
		return converter.convert(userDTOList, SysUserResp.class);
	}

	/**
	 * 根据角色ID查询用户列表
	 */
	@GetMapping("/by-role")
	public List<SysUserResp> getUsersByRoleId(@RequestParam @NotNull Long roleId) {
		List<SysUserDTO> userDTOList = sysUserService.getUsersByRoleId(roleId);
		return converter.convert(userDTOList, SysUserResp.class);
	}

	/**
	 * 重置用户密码
	 */
	@PostMapping("/reset-password")
	@SaCheckPermission(PermissionConstants.User.RESET_PASSWORD)
	public void resetPassword(@RequestParam @NotNull Long userId, @RequestParam String newPassword) {
		sysUserService.resetPassword(userId, newPassword);
	}

	/**
	 * 修改用户密码（用户只能修改自己的密码）
	 */
	@PostMapping("/change-password")
	public void changePassword(@RequestParam @NotNull Long userId, @RequestParam String oldPassword,
			@RequestParam String newPassword) {
		// 用户只能修改自己的密码，或者管理员可以修改任何人的密码
		Long currentUserId = StpUtil.getLoginIdAsLong();
		if (!currentUserId.equals(userId)) {
			StpUtil.checkPermission(PermissionConstants.User.CHANGE_PASSWORD);
		}

		sysUserService.changePassword(userId, oldPassword, newPassword);
	}

	/**
	 * 更新用户状态
	 */
	@PostMapping("/update-status")
	@SaCheckPermission(PermissionConstants.User.UPDATE_STATUS)
	public void updateUserStatus(@RequestParam @NotNull Long userId, @RequestParam String status) {
		sysUserService.updateUserStatus(userId, status);
	}

	/**
	 * 分配用户角色
	 */
	@PostMapping("/assign-roles")
	@SaCheckPermission(PermissionConstants.User.ASSIGN_ROLES)
	public void assignUserRoles(@RequestParam @NotNull Long userId, @RequestBody List<Long> roleIds) {
		sysUserService.assignUserRoles(userId, roleIds);
	}

	/**
	 * 查询用户权限
	 */
	@GetMapping("/permissions")
	public Set<String> getUserPermissions(@RequestParam @NotNull Long userId) {
		return sysUserService.getUserPermissions(userId);
	}

	/**
	 * 查询用户角色
	 */
	@GetMapping("/roles")
	public Set<String> getUserRoles(@RequestParam @NotNull Long userId) {
		return sysUserService.getUserRoles(userId);
	}

	/**
	 * 检查用户名是否存在
	 */
	@GetMapping("/exists-username")
	public Boolean existsByUsername(@RequestParam String username, @RequestParam(required = false) Long excludeId) {
		return sysUserService.existsByUsername(username, excludeId);
	}

	/**
	 * 检查邮箱是否存在
	 */
	@GetMapping("/exists-email")
	public Boolean existsByEmail(@RequestParam String email, @RequestParam(required = false) Long excludeId) {
		return sysUserService.existsByEmail(email, excludeId);
	}

	/**
	 * 检查手机号是否存在
	 */
	@GetMapping("/exists-phone")
	public Boolean existsByPhone(@RequestParam String phone, @RequestParam(required = false) Long excludeId) {
		return sysUserService.existsByPhone(phone, excludeId);
	}

	/**
	 * 锁定用户
	 */
	@PostMapping("/lock")
	@SaCheckPermission(PermissionConstants.User.LOCK)
	public void lockUser(@RequestParam @NotNull Long userId) {
		sysUserService.lockUser(userId);
	}

	/**
	 * 解锁用户
	 */
	@PostMapping("/unlock")
	@SaCheckPermission(PermissionConstants.User.UNLOCK)
	public void unlockUser(@RequestParam @NotNull Long userId) {
		sysUserService.unlockUser(userId);
	}

}
