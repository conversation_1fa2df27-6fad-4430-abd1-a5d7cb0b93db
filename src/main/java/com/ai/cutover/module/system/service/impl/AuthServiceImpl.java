package com.ai.cutover.module.system.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.captcha.CaptchaUtil;
import cn.hutool.captcha.LineCaptcha;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.ai.cutover.common.constant.CacheConstants;
import com.ai.cutover.common.constant.StatusConstants;
import com.ai.cutover.common.util.Assert;
import com.ai.cutover.common.constant.ErrorMessages;
import com.ai.cutover.common.service.RedisService;
import com.ai.cutover.module.system.model.entity.SysUser;
import com.ai.cutover.module.system.model.dto.SysDeptDTO;
import com.ai.cutover.module.system.model.dto.SysMenuDTO;
import com.ai.cutover.module.system.model.dto.SysUserDTO;
import com.ai.cutover.module.system.model.req.ChangePasswordReq;
import com.ai.cutover.module.system.model.req.LoginReq;
import com.ai.cutover.module.system.model.req.ResetPasswordReq;
import com.ai.cutover.module.system.model.resp.CaptchaResp;
import com.ai.cutover.module.system.model.resp.CaptchaVerifyResp;
import com.ai.cutover.module.system.model.resp.CurrentUserResp;
import com.ai.cutover.module.system.model.resp.LoginResp;
import com.ai.cutover.module.system.service.*;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 认证服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthServiceImpl implements AuthService {

	private final SysUserService sysUserService;

	private final SysDeptService sysDeptService;

	private final SysMenuService sysMenuService;

	private final Converter converter;

	private final RedisService redisService;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public LoginResp login(LoginReq request, String loginIp) {
		Assert.hasText(request.getUsername(), ErrorMessages.Common.PARAM_MISSING);
		Assert.hasText(request.getPassword(), ErrorMessages.Common.PARAM_MISSING);

		// 检查用户是否被锁定
		String lockKey = CacheConstants.Key.Auth.LOGIN_LOCK + request.getUsername();
		Boolean isLocked = redisService.hasKey(lockKey);
		Assert.isFalse(Boolean.TRUE.equals(isLocked), ErrorMessages.User.ACCOUNT_LOCKED,
				CacheConstants.Config.LOGIN_LOCK_MINUTES);

		// 验证验证码
		if (StrUtil.isNotBlank(request.getCaptchaKey()) && StrUtil.isNotBlank(request.getCaptcha())) {
			CaptchaVerifyResp verifyResult = verifyCaptcha(request.getCaptchaKey(), request.getCaptcha());
			Assert.isTrue(verifyResult.getValid(), ErrorMessages.User.CAPTCHA_ERROR);
		}

		// 查询用户并验证状态
		SysUserDTO userDTO = sysUserService.getUserByUsername(request.getUsername());
		Assert.notNull(userDTO, ErrorMessages.User.USER_NOT_FOUND);
		Assert.isTrue(StatusConstants.User.NORMAL.equals(userDTO.getStatus()),
				ErrorMessages.User.USER_DISABLED_OR_LOCKED);

		// 4. 验证密码
		SysUser user = sysUserService.getUserEntityById(userDTO.getId());
		boolean passwordValid = sysUserService.verifyPassword(request.getPassword(), user.getPassword());

		if (!passwordValid) {
			// 密码错误，增加失败次数
			handleLoginFailure(request.getUsername());
		}

		// 5. 登录成功处理
		clearLoginFailCount(request.getUsername());
		sysUserService.updateLastLoginInfo(userDTO.getId(), loginIp);
		StpUtil.login(userDTO.getId());

		return buildLoginResponse(userDTO);
	}

	@Override
	public void logout(Long userId) {
		StpUtil.logout(userId);
	}

	@Override
	public CurrentUserResp getCurrentUser() {
		Long userId = StpUtil.getLoginIdAsLong();
		SysUserDTO userDTO = sysUserService.getUserById(userId);
		Assert.notNull(userDTO, ErrorMessages.User.USER_NOT_FOUND);

		// 获取部门信息
		String deptName = getDeptNameByUserId(userDTO.getDeptId());

		// 获取用户角色、权限和菜单
		Set<String> roles = sysUserService.getUserRoles(userId);
		Set<String> permissions = sysUserService.getUserPermissions(userId);
		List<SysMenuDTO> menuDTOList = sysMenuService.getMenusByUserId(userId);
		List<CurrentUserResp.MenuInfo> menus = convertToMenuInfoList(menuDTOList);

		// 构建响应
		CurrentUserResp response = converter.convert(userDTO, CurrentUserResp.class);
		response.setDeptName(deptName);
		response.setRoles(roles);
		response.setPermissions(permissions);
		response.setMenus(menus);

		return response;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void changePassword(Long userId, ChangePasswordReq request) {
		Assert.hasText(request.getOldPassword(), ErrorMessages.User.PASSWORD_REQUIRED);
		Assert.hasText(request.getNewPassword(), ErrorMessages.User.PASSWORD_REQUIRED);
		Assert.hasText(request.getConfirmPassword(), ErrorMessages.User.PASSWORD_REQUIRED);
		Assert.isTrue(request.getNewPassword().equals(request.getConfirmPassword()),
				ErrorMessages.User.PASSWORD_CONFIRM_NOT_MATCH);

		sysUserService.changePassword(userId, request.getOldPassword(), request.getNewPassword());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void resetPassword(ResetPasswordReq request) {
		Assert.notNull(request.getUserId(), ErrorMessages.Common.PARAM_MISSING);
		Assert.hasText(request.getNewPassword(), ErrorMessages.User.PASSWORD_REQUIRED);

		sysUserService.resetPassword(request.getUserId(), request.getNewPassword());
	}

	@Override
	public CaptchaResp generateCaptcha() {
		String captchaKey = IdUtil.simpleUUID();

		LineCaptcha lineCaptcha = CaptchaUtil.createLineCaptcha(200, 100, 4, 80);
		String captchaCode = lineCaptcha.getCode();
		String captchaImage = lineCaptcha.getImageBase64Data();

		// 存储验证码到Redis（5分钟过期）
		String cacheKey = CacheConstants.Key.Auth.CAPTCHA + captchaKey;
		redisService.set(cacheKey, captchaCode, CacheConstants.Config.CAPTCHA_EXPIRE_MINUTES, TimeUnit.MINUTES);

		log.debug("生成验证码: captchaKey={}, captchaCode={}", captchaKey, captchaCode);

		return CaptchaResp.builder()
			.captchaKey(captchaKey)
			.captchaImage(captchaImage)
			.expireTime(CacheConstants.Config.CAPTCHA_EXPIRE_MINUTES * 60)
			.build();
	}

	@Override
	public CaptchaVerifyResp verifyCaptcha(String captchaKey, String captchaCode) {
		if (StrUtil.isBlank(captchaKey) || StrUtil.isBlank(captchaCode)) {
			return CaptchaVerifyResp.of(false, ErrorMessages.User.CAPTCHA_REQUIRED);
		}

		// 从Redis获取验证码
		String cacheKey = CacheConstants.Key.Auth.CAPTCHA + captchaKey;
		String storedCode = redisService.get(cacheKey, String.class);

		if (storedCode == null) {
			return CaptchaVerifyResp.of(false, ErrorMessages.User.CAPTCHA_EXPIRED);
		}

		// 检查验证码错误次数
		String errorCountKey = CacheConstants.Key.Auth.CAPTCHA_ERROR_COUNT + captchaKey;
		Long errorCount = redisService.getLong(errorCountKey);

		// 如果错误次数过多，删除验证码
		if (errorCount >= CacheConstants.Limit.MAX_CAPTCHA_ERROR_COUNT) {
			redisService.delete(cacheKey);
			redisService.delete(errorCountKey);
			return CaptchaVerifyResp.of(false, "验证码错误次数过多，请重新获取");
		}

		boolean isValid = captchaCode.equalsIgnoreCase(storedCode);
		log.debug("验证码校验: captchaKey={}, inputCode={}, storedCode={}, isValid={}", captchaKey, captchaCode, storedCode,
				isValid);

		if (isValid) {
			// 验证成功，删除验证码和错误计数
			redisService.delete(cacheKey);
			redisService.delete(errorCountKey);
			return CaptchaVerifyResp.of(true, "验证通过");
		}
		else {
			// 验证失败，增加错误次数，但保留验证码
			errorCount++;
			redisService.set(errorCountKey, errorCount, CacheConstants.Config.CAPTCHA_EXPIRE_MINUTES, TimeUnit.MINUTES);

			long remainingAttempts = CacheConstants.Limit.MAX_CAPTCHA_ERROR_COUNT - errorCount;
			String errorMessage = remainingAttempts > 0 ? StrUtil.format("验证码错误，还可尝试{}次", remainingAttempts)
					: "验证码错误次数过多，请重新获取";

			return CaptchaVerifyResp.of(false, errorMessage);
		}
	}

	/**
	 * 构建登录响应
	 */
	private LoginResp buildLoginResponse(SysUserDTO userDTO) {
		// 获取部门名称
		String deptName = getDeptNameByUserId(userDTO.getDeptId());

		String token = StpUtil.getTokenValue();
		return LoginResp.builder()
			.accessToken(token)
			.tokenType("Bearer")
			.expiresIn(StpUtil.getTokenTimeout())
			.userId(userDTO.getId())
			.username(userDTO.getUsername())
			.realName(userDTO.getRealName())
			.avatar(userDTO.getAvatar())
			.email(userDTO.getEmail())
			.phone(userDTO.getPhone())
			.deptId(userDTO.getDeptId())
			.deptName(deptName)
			.roles(sysUserService.getUserRoles(userDTO.getId()))
			.permissions(sysUserService.getUserPermissions(userDTO.getId()))
			.lastLoginTime(userDTO.getLastLoginTime())
			.lastLoginIp(userDTO.getLastLoginIp())
			.build();
	}

	/**
	 * 转换菜单DTO列表为菜单信息列表（递归处理子菜单）
	 */
	private List<CurrentUserResp.MenuInfo> convertToMenuInfoList(List<SysMenuDTO> menuDTOList) {
		if (menuDTOList == null || menuDTOList.isEmpty()) {
			return List.of();
		}

		return menuDTOList.stream().map(menuDTO -> {
			CurrentUserResp.MenuInfo.MenuInfoBuilder builder = CurrentUserResp.MenuInfo.builder()
				.id(menuDTO.getId())
				.menuCode(menuDTO.getPermission())
				.menuName(menuDTO.getMenuName())
				.menuType(menuDTO.getMenuType())
				.path(menuDTO.getPath())
				.component(menuDTO.getComponent())
				.icon(menuDTO.getIcon())
				.sortOrder(menuDTO.getSortOrder())
				.parentId(menuDTO.getParentId());

			// 递归处理子菜单
			if (menuDTO.getChildren() != null && !menuDTO.getChildren().isEmpty()) {
				builder.children(convertToMenuInfoList(menuDTO.getChildren()));
			}

			return builder.build();
		}).collect(Collectors.toList());
	}

	/**
	 * 处理登录失败
	 */
	private void handleLoginFailure(String username) {
		String failCountKey = CacheConstants.Key.Auth.LOGIN_FAIL_COUNT + username;

		// 获取当前失败次数
		Long failCount = redisService.getLong(failCountKey);

		// 增加失败次数
		failCount++;
		redisService.set(failCountKey, failCount, CacheConstants.Config.LOGIN_LOCK_MINUTES, TimeUnit.MINUTES);

		// 如果失败次数达到上限，锁定账户
		if (failCount >= CacheConstants.Limit.MAX_LOGIN_FAIL_COUNT) {
			String lockKey = CacheConstants.Key.Auth.LOGIN_LOCK + username;
			redisService.set(lockKey, LocalDateTime.now().toString(), CacheConstants.Config.LOGIN_LOCK_MINUTES,
					TimeUnit.MINUTES);
			log.warn("用户登录失败次数过多，账户已被锁定: username={}, failCount={}", username, failCount);

			// 抛出锁定异常
			Assert.fail(ErrorMessages.User.ACCOUNT_LOCKED, CacheConstants.Config.LOGIN_LOCK_MINUTES);
		}

		// 提示剩余尝试次数
		long remainingAttempts = CacheConstants.Limit.MAX_LOGIN_FAIL_COUNT - failCount;
		log.warn("用户登录失败: username={}, failCount={}, remainingAttempts={}", username, failCount, remainingAttempts);

		// 抛出包含剩余次数信息的异常
		Assert.fail(ErrorMessages.User.LOGIN_FAIL_COUNT_WARNING, failCount, remainingAttempts);
	}

	/**
	 * 清除登录失败次数
	 */
	private void clearLoginFailCount(String username) {
		String failCountKey = CacheConstants.Key.Auth.LOGIN_FAIL_COUNT + username;
		redisService.delete(failCountKey);
	}

	/**
	 * 清除验证码相关缓存
	 */
	private void clearCaptchaCache(String captchaKey) {
		String cacheKey = CacheConstants.Key.Auth.CAPTCHA + captchaKey;
		String errorCountKey = CacheConstants.Key.Auth.CAPTCHA_ERROR_COUNT + captchaKey;
		redisService.delete(cacheKey);
		redisService.delete(errorCountKey);
	}

	/**
	 * 根据部门ID获取部门名称
	 * @param deptId 部门ID
	 * @return 部门名称，如果部门不存在则返回null
	 */
	private String getDeptNameByUserId(Long deptId) {
		if (deptId == null) {
			return null;
		}

		SysDeptDTO deptDTO = sysDeptService.getDeptById(deptId);
		return deptDTO != null ? deptDTO.getDeptName() : null;
	}

}
