package com.ai.cutover.module.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ai.cutover.common.constant.BusinessConstants;
import com.ai.cutover.common.constant.StatusConstants;
import com.ai.cutover.common.util.Assert;
import com.ai.cutover.common.constant.CommonConstants;
import com.ai.cutover.common.constant.ErrorMessages;
import com.ai.cutover.module.system.dao.SysDeptDao;
import com.ai.cutover.module.system.dao.SysUserDao;
import com.ai.cutover.module.system.model.entity.SysDept;
import com.ai.cutover.module.system.model.dto.SysDeptDTO;
import com.ai.cutover.module.system.model.entity.SysUser;
import com.ai.cutover.module.system.model.req.CreateDeptReq;
import com.ai.cutover.module.system.model.req.DeptQueryReq;
import com.ai.cutover.module.system.model.req.UpdateDeptReq;
import com.ai.cutover.module.system.service.SysDeptService;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.row.Db;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 系统部门服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysDeptServiceImpl implements SysDeptService {

	private final SysDeptDao sysDeptDao;

	private final SysUserDao sysUserDao;

	private final Converter converter;

	@Override
	@Transactional
	public SysDeptDTO createDept(CreateDeptReq request) {
		// 1. 参数校验
		Assert.hasText(request.getDeptCode(), ErrorMessages.Common.PARAM_MISSING);
		Assert.hasText(request.getDeptName(), ErrorMessages.Common.PARAM_MISSING);
		Assert.notNull(request.getParentId(), ErrorMessages.Common.PARAM_MISSING);

		// 2. 检查部门编码是否重复
		Assert.isFalse(existsByDeptCode(request.getDeptCode(), null), ErrorMessages.Dept.DEPT_CODE_ALREADY_EXISTS);

		// 3. 检查部门名称是否重复（同级）
		Assert.isFalse(existsByDeptName(request.getDeptName(), request.getParentId(), null),
				ErrorMessages.Dept.DEPT_NAME_ALREADY_EXISTS);

		// 4. 检查父部门是否存在（非根部门）
		if (!request.getParentId().equals(BusinessConstants.DefaultValue.ROOT_DEPT_ID)) {
			SysDept parentDept = sysDeptDao.selectOneById(request.getParentId());
			Assert.notNull(parentDept, ErrorMessages.Dept.PARENT_DEPT_NOT_FOUND);
			Assert.isTrue(StatusConstants.Dept.NORMAL.equals(parentDept.getStatus()), ErrorMessages.Dept.DEPT_DISABLED);
		}

		// 5. 转换为实体并设置默认值
		SysDept dept = converter.convert(request, SysDept.class);
		dept.setStatus(StatusConstants.Dept.NORMAL);

		// 6. 计算层级和祖先路径
		calculateDeptHierarchy(dept);

		// 7. 保存部门
		sysDeptDao.insert(dept);

		return converter.convert(dept, SysDeptDTO.class);
	}

	@Override
	@Transactional
	public SysDeptDTO updateDept(UpdateDeptReq request) {
		// 1. 参数校验
		Assert.notNull(request.getId(), ErrorMessages.Common.PARAM_MISSING);
		Assert.hasText(request.getDeptCode(), ErrorMessages.Common.PARAM_MISSING);
		Assert.hasText(request.getDeptName(), ErrorMessages.Common.PARAM_MISSING);

		// 2. 检查部门是否存在
		SysDept existingDept = sysDeptDao.selectOneById(request.getId());
		Assert.notNull(existingDept, ErrorMessages.Dept.DEPT_NOT_FOUND);

		// 3. 检查部门编码是否重复

		Assert.isFalse(existsByDeptCode(request.getDeptCode(), request.getId()),
				ErrorMessages.Dept.DEPT_CODE_ALREADY_EXISTS);

		// 5. 检查部门名称是否重复（同级）
		Assert.isFalse(existsByDeptName(request.getDeptName(), request.getParentId(), request.getId()),
				ErrorMessages.Dept.DEPT_NAME_ALREADY_EXISTS);

		// 6. 检查父部门变更的合法性
		if (request.getParentId() != null && !request.getParentId().equals(existingDept.getParentId())) {
			validateParentDeptChange(request.getId(), request.getParentId());
		}

		// 7. 更新部门信息
		SysDept dept = converter.convert(request, SysDept.class);

		// 8. 重新计算层级和祖先路径（如果父部门发生变化）
		if (request.getParentId() != null && !request.getParentId().equals(existingDept.getParentId())) {
			calculateDeptHierarchy(dept);
			// 更新所有子部门的层级和祖先路径
			updateChildrenHierarchy(dept.getId());
		}

		sysDeptDao.update(dept);

		return converter.convert(dept, SysDeptDTO.class);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteDept(Long deptId) {
		// 1. 参数校验
		Assert.notNull(deptId, ErrorMessages.Common.PARAM_MISSING);

		// 2. 检查部门是否存在
		SysDept dept = sysDeptDao.selectOneById(deptId);
		Assert.notNull(dept, ErrorMessages.Dept.DEPT_NOT_FOUND);

		// 3. 检查是否有子部门
		long childrenCount = getChildrenCount(deptId);
		Assert.isFalse(childrenCount > 0, ErrorMessages.Dept.DEPT_HAS_CHILDREN, dept.getDeptName(), childrenCount);

		// 4. 检查是否有用户
		Integer userCount = countUsersByDeptId(deptId);
		Assert.isTrue(userCount == 0, ErrorMessages.Dept.DEPT_HAS_USERS, dept.getDeptName(), userCount);

		// 5. 删除部门
		sysDeptDao.deleteById(deptId);
	}

	@Override
	public SysDeptDTO getDeptById(Long deptId) {
		Assert.notNull(deptId, ErrorMessages.Common.PARAM_MISSING);

		SysDept dept = sysDeptDao.selectOneById(deptId);
		Assert.notNull(dept, ErrorMessages.Dept.DEPT_NOT_FOUND);

		SysDeptDTO deptDTO = converter.convert(dept, SysDeptDTO.class);

		// 填充扩展字段
		fillDeptExtendedFields(List.of(deptDTO));

		return deptDTO;
	}

	@Override
	public SysDeptDTO getDeptByCode(String deptCode) {
		Assert.hasText(deptCode, ErrorMessages.Common.PARAM_MISSING);

		SysDept dept = QueryChain.of(sysDeptDao)
			.where(SysDept::getDeptCode)
			.eq(deptCode)
			.and(SysDept::getStatus)
			.eq(StatusConstants.Dept.NORMAL)
			.one();
		Assert.notNull(dept, ErrorMessages.Dept.DEPT_NOT_FOUND);

		SysDeptDTO deptDTO = converter.convert(dept, SysDeptDTO.class);

		// 填充扩展字段
		fillDeptExtendedFields(List.of(deptDTO));

		return deptDTO;
	}

	@Override
	public List<SysDeptDTO> getDeptTree(DeptQueryReq request) {
		List<SysDept> deptList = QueryChain.of(sysDeptDao)
			.where(SysDept::getDeptName)
			.like(request.getDeptName(), StrUtil.isNotBlank(request.getDeptName()))
			.and(SysDept::getDeptCode)
			.like(request.getDeptCode(), StrUtil.isNotBlank(request.getDeptCode()))
			.and(SysDept::getStatus)
			.eq(request.getStatus(), StrUtil.isNotBlank(request.getStatus()))
			.orderBy(SysDept::getSortOrder, true)
			.orderBy(SysDept::getId, true)
			.list();
		List<SysDeptDTO> deptDTOList = converter.convert(deptList, SysDeptDTO.class);

		// 填充扩展字段
		fillDeptExtendedFields(deptDTOList);

		return buildDeptTree(deptDTOList);
	}

	@Override
	public List<SysDeptDTO> getDeptsByParentId(Long parentId) {
		Assert.notNull(parentId, ErrorMessages.Common.PARAM_MISSING);

		List<SysDept> deptList = QueryChain.of(sysDeptDao)
			.where(SysDept::getParentId)
			.eq(parentId)
			.and(SysDept::getStatus)
			.eq(StatusConstants.Dept.NORMAL)
			.orderBy(SysDept::getSortOrder, true)
			.orderBy(SysDept::getId, true)
			.list();

		List<SysDeptDTO> deptDTOList = converter.convert(deptList, SysDeptDTO.class);

		// 填充扩展字段
		fillDeptExtendedFields(deptDTOList);

		return deptDTOList;
	}

	@Override
	public List<Long> getChildrenIds(Long deptId) {
		Assert.notNull(deptId, ErrorMessages.Common.PARAM_MISSING);

		List<Long> result = new ArrayList<>();
		result.add(deptId);

		collectChildrenIds(deptId, result);

		return result;
	}

	@Override
	public List<Long> getParentIds(Long deptId) {
		Assert.notNull(deptId, ErrorMessages.Common.PARAM_MISSING);

		List<Long> result = new ArrayList<>();
		SysDept dept = sysDeptDao.selectOneById(deptId);

		if (dept != null && StrUtil.isNotBlank(dept.getAncestors())) {
			String[] ancestors = dept.getAncestors().split(StrUtil.COMMA);
			for (String ancestor : ancestors) {
				if (StrUtil.isNotBlank(ancestor) && !ancestor.equals(CommonConstants.NumberConstants.ZERO_STR)) {
					result.add(Long.valueOf(ancestor));
				}
			}
		}

		return result;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateDeptStatus(Long deptId, String status) {
		Assert.notNull(deptId, ErrorMessages.Common.PARAM_MISSING);
		Assert.hasText(status, ErrorMessages.Common.PARAM_MISSING);

		SysDept dept = sysDeptDao.selectOneById(deptId);
		Assert.notNull(dept, ErrorMessages.Dept.DEPT_NOT_FOUND);

		dept.setStatus(status);
		sysDeptDao.update(dept);
	}

	@Override
	public boolean existsByDeptCode(String deptCode, Long excludeId) {
		Assert.hasText(deptCode, ErrorMessages.Common.PARAM_MISSING);

		return QueryChain.of(sysDeptDao)
			.where(SysDept::getDeptCode)
			.eq(deptCode)
			.and(SysDept::getId)
			.ne(excludeId, excludeId != null)
			.exists();
	}

	@Override
	public boolean existsByDeptName(String deptName, Long parentId, Long excludeId) {
		Assert.hasText(deptName, ErrorMessages.Common.PARAM_MISSING);
		Assert.notNull(parentId, ErrorMessages.Common.PARAM_MISSING);

		return QueryChain.of(sysDeptDao)
			.where(SysDept::getDeptName)
			.eq(deptName)
			.and(SysDept::getParentId)
			.eq(parentId)
			.and(SysDept::getId)
			.ne(excludeId, excludeId != null)
			.exists();
	}

	@Override
	public boolean hasChildren(Long deptId) {
		Assert.notNull(deptId, ErrorMessages.Common.PARAM_MISSING);

		return QueryChain.of(sysDeptDao).where(SysDept::getParentId).eq(deptId).exists();
	}

	/**
	 * 获取子部门数量
	 */
	private long getChildrenCount(Long deptId) {
		return QueryChain.of(sysDeptDao).where(SysDept::getParentId).eq(deptId).count();
	}

	@Override
	public Integer countUsersByDeptId(Long deptId) {
		Assert.notNull(deptId, ErrorMessages.Common.PARAM_MISSING);
		long count = QueryChain.of(SysUser.class).where(SysUser::getDeptId).eq(deptId).count();
		return Math.toIntExact(count);
	}

	@Override
	public List<SysDeptDTO> buildDeptTree(List<SysDeptDTO> deptList) {
		if (CollUtil.isEmpty(deptList)) {
			return new ArrayList<>();
		}

		// 按ID分组，便于查找
		Map<Long, SysDeptDTO> deptMap = deptList.stream().collect(Collectors.toMap(SysDeptDTO::getId, dept -> dept));

		List<SysDeptDTO> rootDepts = new ArrayList<>();

		for (SysDeptDTO dept : deptList) {
			if (dept.getParentId() == null || dept.getParentId().equals(BusinessConstants.DefaultValue.ROOT_DEPT_ID)) {
				// 根部门
				rootDepts.add(dept);
			}
			else {
				// 子部门
				SysDeptDTO parent = deptMap.get(dept.getParentId());
				if (parent != null) {
					if (parent.getChildren() == null) {
						parent.setChildren(new ArrayList<>());
					}
					parent.getChildren().add(dept);
				}
			}
		}

		return rootDepts;
	}

	/**
	 * 计算部门层级和祖先路径
	 */
	private void calculateDeptHierarchy(SysDept dept) {
		if (dept.getParentId() == null || dept.getParentId().equals(BusinessConstants.DefaultValue.ROOT_DEPT_ID)) {
			// 根部门
			dept.setLevel(CommonConstants.NumberConstants.ONE);
			dept.setAncestors(BusinessConstants.DefaultValue.ROOT_DEPT_ANCESTORS);
		}
		else {
			// 子部门
			SysDept parentDept = sysDeptDao.selectOneById(dept.getParentId());
			if (parentDept != null) {
				dept.setLevel(parentDept.getLevel() + CommonConstants.NumberConstants.ONE);
				dept.setAncestors(
						parentDept.getAncestors() + CommonConstants.StringConstants.COMMA + parentDept.getId());
			}
		}
	}

	/**
	 * 验证父部门变更的合法性
	 */
	private void validateParentDeptChange(Long deptId, Long newParentId) {
		// 不能将自己设置为父部门
		Assert.isFalse(deptId.equals(newParentId), ErrorMessages.Dept.CANNOT_SET_SELF_AS_PARENT);

		// 不能将子部门设置为父部门
		List<Long> childrenIds = getChildrenIds(deptId);
		Assert.isFalse(childrenIds.contains(newParentId), ErrorMessages.Dept.CANNOT_SET_CHILD_AS_PARENT);

		// 检查新父部门是否存在且正常
		if (!newParentId.equals(BusinessConstants.DefaultValue.ROOT_DEPT_ID)) {
			SysDept newParentDept = sysDeptDao.selectOneById(newParentId);
			Assert.notNull(newParentDept, ErrorMessages.Dept.PARENT_DEPT_NOT_FOUND);
			Assert.isTrue(StatusConstants.Dept.NORMAL.equals(newParentDept.getStatus()),
					ErrorMessages.Dept.DEPT_DISABLED);
		}
	}

	/**
	 * 更新子部门的层级和祖先路径
	 */
	private void updateChildrenHierarchy(Long deptId) {
		List<SysDept> children = QueryChain.of(sysDeptDao).where(SysDept::getParentId).eq(deptId).list();

		if (CollUtil.isNotEmpty(children)) {
			// 1. 计算所有子部门的层级和祖先路径
			for (SysDept child : children) {
				calculateDeptHierarchy(child);
			}

			// 2. 批量更新数据库 - 使用高性能批量更新
			Db.updateEntitiesBatch(children, 1000);

			// 3. 递归更新子部门的子部门
			for (SysDept child : children) {
				updateChildrenHierarchy(child.getId());
			}
		}
	}

	/**
	 * 递归收集所有子部门ID
	 */
	private void collectChildrenIds(Long deptId, List<Long> result) {
		List<SysDept> children = QueryChain.of(sysDeptDao).where(SysDept::getParentId).eq(deptId).list();
		for (SysDept child : children) {
			result.add(child.getId());
			collectChildrenIds(child.getId(), result);
		}
	}

	/**
	 * 填充部门扩展字段
	 */
	private void fillDeptExtendedFields(List<SysDeptDTO> deptDTOList) {
		if (CollUtil.isEmpty(deptDTOList)) {
			return;
		}

		// 1. 批量查询负责人信息
		List<Long> leaderIds = deptDTOList.stream()
			.map(SysDeptDTO::getLeaderId)
			.filter(Objects::nonNull)
			.distinct()
			.collect(Collectors.toList());

		Map<Long, String> leaderNameMap = Map.of();
		if (CollUtil.isNotEmpty(leaderIds)) {
			List<SysUser> leaders = sysUserDao.selectListByIds(leaderIds);
			leaderNameMap = leaders.stream().collect(Collectors.toMap(SysUser::getId, SysUser::getRealName));
		}

		// 2. 批量查询父部门信息
		List<Long> parentIds = deptDTOList.stream()
			.map(SysDeptDTO::getParentId)
			.filter(Objects::nonNull)
			.filter(parentId -> !parentId.equals(BusinessConstants.DefaultValue.ROOT_DEPT_ID))
			.distinct()
			.collect(Collectors.toList());

		Map<Long, String> parentNameMap = Map.of();
		if (CollUtil.isNotEmpty(parentIds)) {
			List<SysDept> parents = sysDeptDao.selectListByIds(parentIds);
			parentNameMap = parents.stream().collect(Collectors.toMap(SysDept::getId, SysDept::getDeptName));
		}

		// 3. 批量查询用户数量
		List<Long> deptIds = deptDTOList.stream().map(SysDeptDTO::getId).collect(Collectors.toList());

		Map<Long, Integer> userCountMap = Map.of();
		if (CollUtil.isNotEmpty(deptIds)) {
			// 简化用户数量查询逻辑
			userCountMap = deptIds.stream()
				.collect(Collectors.toMap(deptId -> deptId,
						deptId -> Math.toIntExact(QueryChain.of(sysUserDao)
							.where(SysUser::getDeptId)
							.eq(deptId)
							.and(SysUser::getStatus)
							.eq(StatusConstants.User.NORMAL)
							.count())));
		}

		// 4. 填充扩展字段
		final Map<Long, String> finalLeaderNameMap = leaderNameMap;
		final Map<Long, String> finalParentNameMap = parentNameMap;
		final Map<Long, Integer> finalUserCountMap = userCountMap;

		deptDTOList.forEach(dept -> {
			// 设置负责人姓名
			if (dept.getLeaderId() != null) {
				dept.setLeaderName(finalLeaderNameMap.get(dept.getLeaderId()));
			}

			// 设置父部门名称
			if (dept.getParentId() != null && !dept.getParentId().equals(BusinessConstants.DefaultValue.ROOT_DEPT_ID)) {
				dept.setParentName(finalParentNameMap.get(dept.getParentId()));
			}

			// 设置用户数量
			Integer userCount = finalUserCountMap.getOrDefault(dept.getId(), 0);
			dept.setUserCount(userCount);

			// 设置是否有子部门
			dept.setHasChildren(hasChildren(dept.getId()));
		});
	}

}
