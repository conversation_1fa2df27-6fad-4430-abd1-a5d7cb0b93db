package com.ai.cutover.module.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import com.ai.cutover.common.constant.BusinessConstants;
import com.ai.cutover.common.constant.StatusConstants;
import com.ai.cutover.common.util.Assert;
import com.ai.cutover.common.constant.ErrorMessages;
import com.ai.cutover.module.system.dao.SysMenuDao;
import com.ai.cutover.module.system.model.entity.SysMenu;
import com.ai.cutover.module.system.model.entity.SysPermission;
import static com.ai.cutover.module.system.model.entity.table.SysMenuTableDef.SYS_MENU;
import static com.ai.cutover.module.system.model.entity.table.SysPermissionTableDef.SYS_PERMISSION;
import static com.ai.cutover.module.system.model.entity.table.SysRolePermissionTableDef.SYS_ROLE_PERMISSION;
import static com.ai.cutover.module.system.model.entity.table.SysUserRoleTableDef.SYS_USER_ROLE;
import com.ai.cutover.module.system.model.dto.SysMenuDTO;
import com.ai.cutover.module.system.model.req.CreateMenuReq;
import com.ai.cutover.module.system.model.req.MenuQueryReq;
import com.ai.cutover.module.system.model.req.UpdateMenuReq;
import com.ai.cutover.module.system.service.SysMenuService;
import com.mybatisflex.core.query.QueryChain;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 系统菜单服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysMenuServiceImpl implements SysMenuService {

	private final SysMenuDao sysMenuDao;

	private final Converter converter;

	@Override
	@Transactional
	public SysMenuDTO createMenu(CreateMenuReq request) {
		// 1. 参数校验
		Assert.hasText(request.getMenuName(), ErrorMessages.Menu.MENU_NAME_NOT_BLANK);
		Assert.hasText(request.getMenuType(), ErrorMessages.Menu.MENU_TYPE_NOT_BLANK);
		Assert.notNull(request.getParentId(), ErrorMessages.General.ID_NOT_NULL);

		// 2. 检查菜单名称是否重复（同级）
		Assert.isFalse(existsByMenuName(request.getMenuName(), request.getParentId(), null),
				ErrorMessages.Menu.MENU_NAME_ALREADY_EXISTS);

		// 3. 检查权限标识是否重复（如果有）
		if (StrUtil.isNotBlank(request.getPermission())) {
			Assert.isFalse(existsByPermission(request.getPermission(), null),
					ErrorMessages.Menu.MENU_PERMISSION_ALREADY_EXISTS);
		}

		// 4. 检查父菜单是否存在（非根菜单）
		if (!request.getParentId().equals(0L)) {
			SysMenu parentMenu = sysMenuDao.selectOneById(request.getParentId());
			Assert.notNull(parentMenu, ErrorMessages.Menu.PARENT_MENU_NOT_FOUND);
			Assert.isTrue(StatusConstants.Menu.NORMAL.equals(parentMenu.getStatus()), ErrorMessages.Menu.MENU_DISABLED);
		}

		// 5. 转换为实体并设置默认值
		SysMenu menu = converter.convert(request, SysMenu.class);
		menu.setStatus(StatusConstants.Menu.NORMAL);
		menu.setVisible(BusinessConstants.YesNo.YES);
		menu.setIsFrame(BusinessConstants.YesNo.NO);
		menu.setIsCache(BusinessConstants.YesNo.NO);

		// 6. 保存菜单
		sysMenuDao.insert(menu);

		return converter.convert(menu, SysMenuDTO.class);
	}

	@Override
	@Transactional
	public SysMenuDTO updateMenu(UpdateMenuReq request) {
		// 1. 参数校验
		Assert.notNull(request.getId(), ErrorMessages.General.ID_NOT_NULL);
		Assert.hasText(request.getMenuName(), ErrorMessages.Menu.MENU_NAME_NOT_BLANK);

		// 2. 检查菜单是否存在
		SysMenu existingMenu = sysMenuDao.selectOneById(request.getId());
		Assert.notNull(existingMenu, ErrorMessages.Menu.MENU_NOT_FOUND);

		// 3. 检查菜单名称是否重复（同级）
		Assert.isFalse(existsByMenuName(request.getMenuName(), request.getParentId(), request.getId()),
				ErrorMessages.Menu.MENU_NAME_ALREADY_EXISTS);

		// 4. 检查权限标识是否重复（如果有）
		if (StrUtil.isNotBlank(request.getPermission())) {
			Assert.isFalse(existsByPermission(request.getPermission(), request.getId()),
					ErrorMessages.Menu.MENU_PERMISSION_ALREADY_EXISTS);
		}

		// 5. 检查父菜单变更的合法性
		if (request.getParentId() != null && !request.getParentId().equals(existingMenu.getParentId())) {
			validateParentMenuChange(request.getId(), request.getParentId());
		}

		// 6. 更新菜单信息
		SysMenu menu = converter.convert(request, SysMenu.class);
		sysMenuDao.update(menu);

		return converter.convert(menu, SysMenuDTO.class);
	}

	@Override
	@Transactional
	public void deleteMenu(Long menuId) {
		// 1. 参数校验
		Assert.notNull(menuId, ErrorMessages.General.ID_NOT_NULL);

		// 2. 检查菜单是否存在
		SysMenu menu = sysMenuDao.selectOneById(menuId);
		Assert.notNull(menu, ErrorMessages.Menu.MENU_NOT_FOUND);

		// 3. 检查是否有子菜单
		long childrenCount = getChildrenCount(menuId);
		Assert.isFalse(childrenCount > 0, ErrorMessages.Menu.MENU_HAS_CHILDREN, menu.getMenuName(), childrenCount);

		// 4. 删除菜单
		sysMenuDao.deleteById(menuId);
	}

	@Override
	public SysMenuDTO getMenuById(Long menuId) {
		Assert.notNull(menuId, ErrorMessages.General.ID_NOT_NULL);

		SysMenu menu = sysMenuDao.selectOneById(menuId);
		Assert.notNull(menu, ErrorMessages.Menu.MENU_NOT_FOUND);

		return converter.convert(menu, SysMenuDTO.class);
	}

	@Override
	public List<SysMenuDTO> getMenuTree(MenuQueryReq request) {
		List<SysMenu> menuList = QueryChain.of(SysMenu.class)
			.where(SysMenu::getMenuName)
			.like(request.getMenuName(), StrUtil.isNotBlank(request.getMenuName()))
			.and(SysMenu::getMenuType)
			.eq(request.getMenuType(), StrUtil.isNotBlank(request.getMenuType()))
			.and(SysMenu::getStatus)
			.eq(request.getStatus(), StrUtil.isNotBlank(request.getStatus()))
			.and(SysMenu::getVisible)
			.eq(request.getVisible(), StrUtil.isNotBlank(request.getVisible()))
			.orderBy(SysMenu::getSortOrder, true)
			.orderBy(SysMenu::getId, true)
			.list();
		List<SysMenuDTO> menuDTOList = converter.convert(menuList, SysMenuDTO.class);

		return buildMenuTree(menuDTOList);
	}

	@Override
	public List<SysMenuDTO> getMenusByParentId(Long parentId) {
		Assert.notNull(parentId, ErrorMessages.General.ID_NOT_NULL);

		List<SysMenu> menuList = QueryChain.of(SysMenu.class)
			.where(SysMenu::getParentId)
			.eq(parentId)
			.and(SysMenu::getStatus)
			.eq(StatusConstants.Menu.NORMAL)
			.orderBy(SysMenu::getSortOrder, true)
			.orderBy(SysMenu::getId, true)
			.list();
		return converter.convert(menuList, SysMenuDTO.class);
	}

	@Override
	public List<SysMenuDTO> getMenusByUserId(Long userId) {
		Assert.notNull(userId, ErrorMessages.General.ID_NOT_NULL);

		// 查询用户拥有的权限标识
		List<String> userPermissions = QueryChain.of(SysPermission.class)
			.select(SYS_PERMISSION.PERMISSION_CODE)
			.leftJoin(SYS_ROLE_PERMISSION)
			.on(SYS_PERMISSION.ID.eq(SYS_ROLE_PERMISSION.PERMISSION_ID))
			.leftJoin(SYS_USER_ROLE)
			.on(SYS_ROLE_PERMISSION.ROLE_ID.eq(SYS_USER_ROLE.ROLE_ID))
			.where(SYS_USER_ROLE.USER_ID.eq(userId))
			.and(SYS_PERMISSION.STATUS.eq(StatusConstants.Permission.NORMAL))
			.listAs(String.class);

		log.debug("用户ID: {}, 拥有的权限: {}", userId, userPermissions);

		return getMenusByPermissions(userPermissions, "用户ID: " + userId);
	}

	@Override
	public List<SysMenuDTO> getMenusByRoleId(Long roleId) {
		Assert.notNull(roleId, ErrorMessages.General.ID_NOT_NULL);

		// 查询角色拥有的权限标识
		List<String> rolePermissions = QueryChain.of(SysPermission.class)
			.select(SYS_PERMISSION.PERMISSION_CODE)
			.leftJoin(SYS_ROLE_PERMISSION)
			.on(SYS_PERMISSION.ID.eq(SYS_ROLE_PERMISSION.PERMISSION_ID))
			.where(SYS_ROLE_PERMISSION.ROLE_ID.eq(roleId))
			.and(SYS_PERMISSION.STATUS.eq(StatusConstants.Permission.NORMAL))
			.listAs(String.class);

		log.debug("角色ID: {}, 拥有的权限: {}", roleId, rolePermissions);

		return getMenusByPermissions(rolePermissions, "角色ID: " + roleId);
	}

	/**
	 * 根据权限标识列表查询菜单并构建菜单树
	 */
	private List<SysMenuDTO> getMenusByPermissions(List<String> permissions, String logPrefix) {
		List<SysMenu> menuList;
		if (CollUtil.isEmpty(permissions)) {
			// 如果没有权限，只显示目录菜单
			menuList = QueryChain.of(SysMenu.class)
				.where(SYS_MENU.STATUS.eq(StatusConstants.Menu.NORMAL))
				.and(SYS_MENU.VISIBLE.eq(BusinessConstants.YesNo.YES))
				.and(SYS_MENU.PERMISSION.isNull().or(SYS_MENU.MENU_TYPE.eq(BusinessConstants.MenuType.DIRECTORY)))
				.orderBy(SYS_MENU.SORT_ORDER, true)
				.orderBy(SYS_MENU.ID, true)
				.list();
		}
		else {
			// 有权限时，查询有权限的菜单和目录菜单
			menuList = QueryChain.of(SysMenu.class)
				.where(SYS_MENU.STATUS.eq(StatusConstants.Menu.NORMAL))
				.and(SYS_MENU.VISIBLE.eq(BusinessConstants.YesNo.YES))
				.and(SYS_MENU.PERMISSION.in(permissions)
					.or(SYS_MENU.PERMISSION.isNull())
					.or(SYS_MENU.MENU_TYPE.eq(BusinessConstants.MenuType.DIRECTORY)))
				.orderBy(SYS_MENU.SORT_ORDER, true)
				.orderBy(SYS_MENU.ID, true)
				.list();
		}

		List<SysMenuDTO> menuDTOList = converter.convert(menuList, SysMenuDTO.class);

		log.debug("{}, 查询到的菜单数量: {}", logPrefix, menuDTOList.size());
		if (log.isDebugEnabled()) {
			menuDTOList.forEach(menu -> log.debug("菜单: {} - {}", menu.getMenuName(), menu.getPermission()));
		}

		return buildMenuTree(menuDTOList);
	}

	@Override
	public List<SysMenuDTO> getAllMenus() {
		List<SysMenu> menuList = QueryChain.of(SysMenu.class)
			.where(SysMenu::getStatus)
			.eq(StatusConstants.Menu.NORMAL)
			.orderBy(SysMenu::getSortOrder, true)
			.orderBy(SysMenu::getId, true)
			.list();
		return converter.convert(menuList, SysMenuDTO.class);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateMenuStatus(Long menuId, String status) {
		Assert.notNull(menuId, ErrorMessages.General.ID_NOT_NULL);
		Assert.hasText(status, ErrorMessages.General.PARAM_NOT_BLANK);

		SysMenu menu = sysMenuDao.selectOneById(menuId);
		Assert.notNull(menu, ErrorMessages.Menu.MENU_NOT_FOUND);

		menu.setStatus(status);
		sysMenuDao.update(menu);
	}

	@Override
	public boolean existsByMenuName(String menuName, Long parentId, Long excludeId) {
		Assert.hasText(menuName, ErrorMessages.Menu.MENU_NAME_NOT_BLANK);
		Assert.notNull(parentId, ErrorMessages.General.ID_NOT_NULL);

		return QueryChain.of(SysMenu.class)
			.where(SysMenu::getMenuName)
			.eq(menuName)
			.and(SysMenu::getParentId)
			.eq(parentId)
			.and(SysMenu::getId)
			.ne(excludeId, excludeId != null)
			.exists();
	}

	@Override
	public boolean existsByPermission(String permission, Long excludeId) {
		Assert.hasText(permission, ErrorMessages.Permission.PERMISSION_CODE_NOT_BLANK);

		return QueryChain.of(SysMenu.class)
			.where(SysMenu::getPermission)
			.eq(permission)
			.and(SysMenu::getId)
			.ne(excludeId, excludeId != null)
			.exists();
	}

	@Override
	public boolean hasChildren(Long menuId) {
		Assert.notNull(menuId, ErrorMessages.General.ID_NOT_NULL);

		return QueryChain.of(SysMenu.class).where(SysMenu::getParentId).eq(menuId).exists();
	}

	/**
	 * 获取子菜单数量
	 */
	private long getChildrenCount(Long menuId) {
		return QueryChain.of(SysMenu.class).where(SysMenu::getParentId).eq(menuId).count();
	}

	@Override
	public List<SysMenuDTO> buildMenuTree(List<SysMenuDTO> menuList) {
		if (CollUtil.isEmpty(menuList)) {
			return new ArrayList<>();
		}

		// 按ID分组，便于查找
		Map<Long, SysMenuDTO> menuMap = menuList.stream().collect(Collectors.toMap(SysMenuDTO::getId, menu -> menu));

		List<SysMenuDTO> rootMenus = new ArrayList<>();

		for (SysMenuDTO menu : menuList) {
			if (menu.getParentId() == null || menu.getParentId().equals(0L)) {
				// 根菜单
				rootMenus.add(menu);
			}
			else {
				// 子菜单
				SysMenuDTO parent = menuMap.get(menu.getParentId());
				if (parent != null) {
					if (parent.getChildren() == null) {
						parent.setChildren(new ArrayList<>());
					}
					parent.getChildren().add(menu);
				}
			}
		}

		return rootMenus;
	}

	/**
	 * 验证父菜单变更的合法性
	 */
	private void validateParentMenuChange(Long menuId, Long newParentId) {
		// 不能将自己设置为父菜单
		Assert.isFalse(menuId.equals(newParentId), ErrorMessages.Menu.CANNOT_SET_SELF_AS_PARENT);

		// 不能将子菜单设置为父菜单
		List<Long> childrenIds = getChildrenIds(menuId);
		Assert.isFalse(childrenIds.contains(newParentId), ErrorMessages.Menu.CANNOT_SET_CHILD_AS_PARENT);

		// 检查新父菜单是否存在且正常
		if (!newParentId.equals(0L)) {
			SysMenu newParentMenu = sysMenuDao.selectOneById(newParentId);
			Assert.notNull(newParentMenu, ErrorMessages.Menu.PARENT_MENU_NOT_FOUND);
			Assert.isTrue(StatusConstants.Menu.NORMAL.equals(newParentMenu.getStatus()),
					ErrorMessages.Menu.MENU_DISABLED);
		}
	}

	/**
	 * 获取所有子菜单ID
	 */
	private List<Long> getChildrenIds(Long menuId) {
		List<Long> result = new ArrayList<>();
		collectChildrenIds(menuId, result);
		return result;
	}

	/**
	 * 递归收集所有子菜单ID
	 */
	private void collectChildrenIds(Long menuId, List<Long> result) {
		List<SysMenu> children = QueryChain.of(SysMenu.class).where(SysMenu::getParentId).eq(menuId).list();
		for (SysMenu child : children) {
			result.add(child.getId());
			collectChildrenIds(child.getId(), result);
		}
	}

}
