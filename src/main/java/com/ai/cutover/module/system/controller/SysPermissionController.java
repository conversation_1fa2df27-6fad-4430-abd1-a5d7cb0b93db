package com.ai.cutover.module.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ai.cutover.module.system.constant.PermissionConstants;
import com.ai.cutover.module.system.model.dto.SysPermissionDTO;
import com.ai.cutover.module.system.service.SysPermissionService;
import com.mybatisflex.core.paginate.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 系统权限控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/system/permission")
@RequiredArgsConstructor
@Validated
public class SysPermissionController {

	private final SysPermissionService sysPermissionService;

	/**
	 * 根据ID查询权限详情
	 */
	@GetMapping("/detail")
	@SaCheckPermission(PermissionConstants.Permission.VIEW)
	public SysPermissionDTO getPermissionDetail(@RequestParam @NotNull Long id) {
		return sysPermissionService.getPermissionById(id);
	}

	/**
	 * 根据权限编码查询权限
	 */
	@GetMapping("/by-code")
	@SaCheckPermission(PermissionConstants.Permission.VIEW)
	public SysPermissionDTO getPermissionByCode(@RequestParam String permissionCode) {
		return sysPermissionService.getPermissionByCode(permissionCode);
	}

	/**
	 * 分页查询权限列表
	 */
	@GetMapping("/page")
	@SaCheckPermission(PermissionConstants.Permission.VIEW)
	public Page<SysPermissionDTO> getPermissionPage(@RequestParam(defaultValue = "1") Integer pageNum,
			@RequestParam(defaultValue = "10") Integer pageSize, @RequestParam(required = false) String permissionCode,
			@RequestParam(required = false) String permissionName,
			@RequestParam(required = false) String permissionType, @RequestParam(required = false) String status) {
		return sysPermissionService.getPermissionPage(pageNum, pageSize, permissionCode, permissionName, permissionType,
				status);
	}

	/**
	 * 根据角色ID查询权限列表
	 */
	@GetMapping("/by-role")
	public List<SysPermissionDTO> getPermissionsByRoleId(@RequestParam @NotNull Long roleId) {
		return sysPermissionService.getPermissionsByRoleId(roleId);
	}

	/**
	 * 根据用户ID查询权限列表
	 */
	@GetMapping("/by-user")
	public List<SysPermissionDTO> getPermissionsByUserId(@RequestParam @NotNull Long userId) {
		return sysPermissionService.getPermissionsByUserId(userId);
	}

	/**
	 * 查询所有权限
	 */
	@GetMapping("/all")
	public List<SysPermissionDTO> getAllPermissions() {
		return sysPermissionService.getAllPermissions();
	}

	/**
	 * 根据权限类型查询权限列表
	 */
	@GetMapping("/by-type")
	public List<SysPermissionDTO> getPermissionsByType(@RequestParam String permissionType) {
		return sysPermissionService.getPermissionsByType(permissionType);
	}

	/**
	 * 检查权限编码是否存在
	 */
	@GetMapping("/exists-code")
	public Boolean existsByPermissionCode(@RequestParam String permissionCode,
			@RequestParam(required = false) Long excludeId) {
		return sysPermissionService.existsByPermissionCode(permissionCode, excludeId);
	}

	/**
	 * 检查权限名称是否存在
	 */
	@GetMapping("/exists-name")
	public Boolean existsByPermissionName(@RequestParam String permissionName,
			@RequestParam(required = false) Long excludeId) {
		return sysPermissionService.existsByPermissionName(permissionName, excludeId);
	}

}
