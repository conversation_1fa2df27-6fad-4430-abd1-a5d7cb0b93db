package com.ai.cutover.module.system.constant;

import lombok.experimental.UtilityClass;

/**
 * 权限常量定义 定义系统中所有的权限标识符
 *
 * <AUTHOR>
 */
@UtilityClass
public class PermissionConstants {

	/**
	 * 用户管理权限
	 */
	@UtilityClass
	public static class User {

		public static final String VIEW = "system:user:view";

		public static final String CREATE = "system:user:create";

		public static final String UPDATE = "system:user:update";

		public static final String DELETE = "system:user:delete";

		public static final String RESET_PASSWORD = "system:user:reset-password";

		public static final String CHANGE_PASSWORD = "system:user:change-password";

		public static final String UPDATE_STATUS = "system:user:update-status";

		public static final String ASSIGN_ROLES = "system:user:assign-roles";

		public static final String LOCK = "system:user:lock";

		public static final String UNLOCK = "system:user:unlock";

	}

	/**
	 * 角色管理权限
	 */
	@UtilityClass
	public static class Role {

		public static final String VIEW = "system:role:view";

		public static final String CREATE = "system:role:create";

		public static final String UPDATE = "system:role:update";

		public static final String DELETE = "system:role:delete";

		public static final String UPDATE_STATUS = "system:role:update-status";

		public static final String ASSIGN_PERMISSIONS = "system:role:assign-permissions";

	}

	/**
	 * 部门管理权限
	 */
	@UtilityClass
	public static class Dept {

		public static final String VIEW = "system:dept:view";

		public static final String CREATE = "system:dept:create";

		public static final String UPDATE = "system:dept:update";

		public static final String DELETE = "system:dept:delete";

		public static final String UPDATE_STATUS = "system:dept:update-status";

	}

	/**
	 * 菜单管理权限
	 */
	@UtilityClass
	public static class Menu {

		public static final String VIEW = "system:menu:view";

		public static final String CREATE = "system:menu:create";

		public static final String UPDATE = "system:menu:update";

		public static final String DELETE = "system:menu:delete";

	}

	/**
	 * 权限管理权限
	 */
	@UtilityClass
	public static class Permission {

		public static final String VIEW = "system:permission:view";

		public static final String CREATE = "system:permission:create";

		public static final String UPDATE = "system:permission:update";

		public static final String DELETE = "system:permission:delete";

	}

}
