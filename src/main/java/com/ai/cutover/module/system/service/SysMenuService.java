package com.ai.cutover.module.system.service;

import com.ai.cutover.module.system.model.dto.SysMenuDTO;
import com.ai.cutover.module.system.model.req.CreateMenuReq;
import com.ai.cutover.module.system.model.req.UpdateMenuReq;
import com.ai.cutover.module.system.model.req.MenuQueryReq;

import java.util.List;

/**
 * 系统菜单服务接口
 *
 * <AUTHOR>
 */
public interface SysMenuService {

	/**
	 * 创建菜单
	 * @param request 创建菜单请求
	 * @return 菜单DTO
	 */
	SysMenuDTO createMenu(CreateMenuReq request);

	/**
	 * 更新菜单
	 * @param request 更新菜单请求
	 * @return 菜单DTO
	 */
	SysMenuDTO updateMenu(UpdateMenuReq request);

	/**
	 * 删除菜单
	 * @param menuId 菜单ID
	 */
	void deleteMenu(Long menuId);

	/**
	 * 根据ID查询菜单
	 * @param menuId 菜单ID
	 * @return 菜单DTO
	 */
	SysMenuDTO getMenuById(Long menuId);

	/**
	 * 查询菜单树形列表
	 * @param request 查询请求
	 * @return 菜单树形列表
	 */
	List<SysMenuDTO> getMenuTree(MenuQueryReq request);

	/**
	 * 根据父菜单ID查询子菜单列表
	 * @param parentId 父菜单ID
	 * @return 子菜单列表
	 */
	List<SysMenuDTO> getMenusByParentId(Long parentId);

	/**
	 * 根据用户ID查询菜单列表
	 * @param userId 用户ID
	 * @return 菜单列表
	 */
	List<SysMenuDTO> getMenusByUserId(Long userId);

	/**
	 * 根据角色ID查询菜单列表
	 * @param roleId 角色ID
	 * @return 菜单列表
	 */
	List<SysMenuDTO> getMenusByRoleId(Long roleId);

	/**
	 * 查询所有正常状态的菜单
	 * @return 菜单列表
	 */
	List<SysMenuDTO> getAllMenus();

	/**
	 * 更新菜单状态
	 * @param menuId 菜单ID
	 * @param status 状态
	 */
	void updateMenuStatus(Long menuId, String status);

	/**
	 * 检查菜单名称是否存在（同级）
	 * @param menuName 菜单名称
	 * @param parentId 父菜单ID
	 * @param excludeId 排除的菜单ID
	 * @return 是否存在
	 */
	boolean existsByMenuName(String menuName, Long parentId, Long excludeId);

	/**
	 * 检查权限标识是否存在
	 * @param permission 权限标识
	 * @param excludeId 排除的菜单ID
	 * @return 是否存在
	 */
	boolean existsByPermission(String permission, Long excludeId);

	/**
	 * 检查菜单是否有子菜单
	 * @param menuId 菜单ID
	 * @return 是否有子菜单
	 */
	boolean hasChildren(Long menuId);

	/**
	 * 构建菜单树形结构
	 * @param menuList 菜单列表
	 * @return 菜单树形列表
	 */
	List<SysMenuDTO> buildMenuTree(List<SysMenuDTO> menuList);

}
