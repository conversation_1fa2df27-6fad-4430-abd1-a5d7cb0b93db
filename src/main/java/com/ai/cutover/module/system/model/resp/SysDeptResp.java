package com.ai.cutover.module.system.model.resp;

import com.ai.cutover.module.system.model.dto.SysDeptDTO;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统部门响应类
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysDeptDTO.class)
public class SysDeptResp {

	/**
	 * 部门ID
	 */
	private Long id;

	/**
	 * 父部门ID（0表示根部门）
	 */
	private Long parentId;

	/**
	 * 部门编码
	 */
	private String deptCode;

	/**
	 * 部门名称
	 */
	private String deptName;

	/**
	 * 部门类型（COMPANY-公司，DEPT-部门，GROUP-小组）
	 */
	private String deptType;

	/**
	 * 显示顺序
	 */
	private Integer sortOrder;

	/**
	 * 负责人ID
	 */
	private Long leaderId;

	/**
	 * 负责人姓名
	 */
	private String leaderName;

	/**
	 * 联系电话
	 */
	private String phone;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 部门地址
	 */
	private String address;

	/**
	 * 部门状态（NORMAL-正常，DISABLED-禁用）
	 */
	private String status;

	/**
	 * 祖级列表（用逗号分隔的父级ID列表）
	 */
	private String ancestors;

	/**
	 * 层级深度
	 */
	private Integer level;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建人
	 */
	private Long createBy;

	/**
	 * 更新人
	 */
	private Long updateBy;

	// 扩展字段

	/**
	 * 父部门名称
	 */
	private String parentName;

	/**
	 * 子部门列表
	 */
	private List<SysDeptResp> children;

	/**
	 * 用户数量
	 */
	private Integer userCount;

	/**
	 * 是否有子部门
	 */
	private Boolean hasChildren;

}
