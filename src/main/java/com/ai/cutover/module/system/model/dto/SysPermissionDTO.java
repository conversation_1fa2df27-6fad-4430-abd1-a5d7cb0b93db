package com.ai.cutover.module.system.model.dto;

import com.ai.cutover.module.system.model.entity.SysPermission;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统权限DTO
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysPermission.class)
public class SysPermissionDTO {

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 权限编码
	 */
	private String permissionCode;

	/**
	 * 权限名称
	 */
	private String permissionName;

	/**
	 * 权限类型
	 */
	private String permissionType;

	/**
	 * 资源路径
	 */
	private String resourcePath;

	/**
	 * 请求方法
	 */
	private String requestMethod;

	/**
	 * 权限描述
	 */
	private String description;

	/**
	 * 显示顺序
	 */
	private Integer sortOrder;

	/**
	 * 权限状态
	 */
	private String status;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

}
