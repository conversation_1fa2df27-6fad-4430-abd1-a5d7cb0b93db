package com.ai.cutover.module.system.model.req;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * 登录请求
 *
 * <AUTHOR>
 */
@Data
public class LoginReq {

	/**
	 * 用户名
	 */
	@NotBlank(message = "用户名不能为空")
	@Size(min = 2, max = 50, message = "用户名长度必须在2-50个字符之间")
	private String username;

	/**
	 * 密码
	 */
	@NotBlank(message = "密码不能为空")
	@Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
	private String password;

	/**
	 * 验证码
	 */
	private String captcha;

	/**
	 * 验证码键值
	 */
	private String captchaKey;

}
