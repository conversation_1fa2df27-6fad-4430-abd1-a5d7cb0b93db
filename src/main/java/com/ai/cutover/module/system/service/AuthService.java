package com.ai.cutover.module.system.service;

import com.ai.cutover.module.system.model.req.ChangePasswordReq;
import com.ai.cutover.module.system.model.req.LoginReq;
import com.ai.cutover.module.system.model.req.ResetPasswordReq;
import com.ai.cutover.module.system.model.resp.CaptchaResp;
import com.ai.cutover.module.system.model.resp.CaptchaVerifyResp;
import com.ai.cutover.module.system.model.resp.CurrentUserResp;
import com.ai.cutover.module.system.model.resp.LoginResp;

/**
 * 认证服务接口
 *
 * <AUTHOR>
 */
public interface AuthService {

	/**
	 * 用户登录
	 * @param request 登录请求
	 * @param loginIp 登录IP
	 * @return 登录响应
	 */
	LoginResp login(LoginReq request, String loginIp);

	/**
	 * 用户登出
	 * @param userId 用户ID
	 */
	void logout(Long userId);

	/**
	 * 获取当前用户信息
	 * @return 当前用户信息
	 */
	CurrentUserResp getCurrentUser();

	/**
	 * 修改密码
	 * @param userId 用户ID
	 * @param request 修改密码请求
	 */
	void changePassword(Long userId, ChangePasswordReq request);

	/**
	 * 重置密码
	 * @param request 重置密码请求
	 */
	void resetPassword(ResetPasswordReq request);

	/**
	 * 生成验证码
	 * @return 验证码信息（包含验证码图片和键值）
	 */
	CaptchaResp generateCaptcha();

	/**
	 * 验证验证码
	 * @param captchaKey 验证码键值
	 * @param captchaCode 验证码
	 * @return 是否验证成功
	 */
	CaptchaVerifyResp verifyCaptcha(String captchaKey, String captchaCode);

}
