package com.ai.cutover.module.system.model.dto;

import com.ai.cutover.module.system.model.entity.SysMenu;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 系统菜单DTO
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysMenu.class)
public class SysMenuDTO {

	/**
	 * 主键ID
	 */
	private Long id;

	/**
	 * 父菜单ID
	 */
	private Long parentId;

	/**
	 * 菜单名称
	 */
	private String menuName;

	/**
	 * 菜单标题
	 */
	private String title;

	/**
	 * 菜单类型
	 */
	private String menuType;

	/**
	 * 路由地址
	 */
	private String path;

	/**
	 * 组件路径
	 */
	private String component;

	/**
	 * 权限标识
	 */
	private String permission;

	/**
	 * 菜单图标
	 */
	private String icon;

	/**
	 * 显示顺序
	 */
	private Integer sortOrder;

	/**
	 * 是否外链
	 */
	private String isFrame;

	/**
	 * 是否缓存
	 */
	private String isCache;

	/**
	 * 是否显示
	 */
	private String visible;

	/**
	 * 菜单状态
	 */
	private String status;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 父菜单名称
	 */
	private String parentName;

	/**
	 * 子菜单列表
	 */
	private List<SysMenuDTO> children;

	/**
	 * 是否有子菜单
	 */
	private Boolean hasChildren;

}
