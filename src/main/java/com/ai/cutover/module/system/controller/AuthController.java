package com.ai.cutover.module.system.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.ai.cutover.common.util.WebUtils;
import com.ai.cutover.module.system.model.req.ChangePasswordReq;
import com.ai.cutover.module.system.model.req.LoginReq;
import com.ai.cutover.module.system.model.req.ResetPasswordReq;
import com.ai.cutover.module.system.model.resp.CaptchaResp;
import com.ai.cutover.module.system.model.resp.CaptchaVerifyResp;
import com.ai.cutover.module.system.model.resp.CurrentUserResp;
import com.ai.cutover.module.system.model.resp.LoginResp;
import com.ai.cutover.module.system.service.AuthService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

/**
 * 认证控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/system/auth")
@RequiredArgsConstructor
@Validated
public class AuthController {

	private final AuthService authService;

	/**
	 * 用户登录
	 */
	@PostMapping("/login")
	public LoginResp login(@Valid @RequestBody LoginReq request, HttpServletRequest httpRequest) {
		String loginIp = WebUtils.getClientIpAddress(httpRequest);
		return authService.login(request, loginIp);
	}

	/**
	 * 用户登出
	 */
	@PostMapping("/logout")
	public void logout() {
		Long userId = StpUtil.getLoginIdAsLong();
		authService.logout(userId);
	}

	/**
	 * 获取当前用户信息
	 */
	@GetMapping("/current-user")
	public CurrentUserResp getCurrentUser() {
		return authService.getCurrentUser();
	}

	/**
	 * 修改密码
	 */
	@PostMapping("/change-password")
	public void changePassword(@Valid @RequestBody ChangePasswordReq request) {
		Long userId = StpUtil.getLoginIdAsLong();
		authService.changePassword(userId, request);
	}

	/**
	 * 重置密码
	 */
	@PostMapping("/reset-password")
	public void resetPassword(@Valid @RequestBody ResetPasswordReq request) {
		authService.resetPassword(request);
	}

	/**
	 * 生成验证码
	 */
	@GetMapping("/captcha")
	public CaptchaResp generateCaptcha() {
		return authService.generateCaptcha();
	}

	/**
	 * 验证验证码
	 */
	@PostMapping("/verify-captcha")
	public CaptchaVerifyResp verifyCaptcha(@RequestParam @NotBlank String captchaKey,
			@RequestParam @NotBlank String captchaCode) {
		return authService.verifyCaptcha(captchaKey, captchaCode);
	}

}
