package com.ai.cutover.module.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.ai.cutover.common.constant.BusinessConstants;
import com.ai.cutover.common.constant.StatusConstants;
import com.ai.cutover.common.enums.UniqueFieldType;
import com.ai.cutover.common.util.Assert;
import com.ai.cutover.common.constant.ErrorMessages;
import com.ai.cutover.module.system.dao.SysRoleDao;
import com.ai.cutover.module.system.dao.SysRolePermissionDao;
import com.ai.cutover.module.system.dao.SysUserRoleDao;
import com.ai.cutover.module.system.model.entity.SysRole;
import com.ai.cutover.module.system.model.entity.SysRolePermission;
import com.ai.cutover.module.system.model.entity.SysUserRole;
import com.ai.cutover.module.system.model.dto.SysRoleDTO;
import com.ai.cutover.module.system.model.req.CreateRoleReq;
import com.ai.cutover.module.system.model.req.UpdateRoleReq;
import com.ai.cutover.module.system.model.req.RoleQueryReq;
import com.ai.cutover.module.system.service.SysRoleService;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.QueryChain;
import com.mybatisflex.core.query.QueryWrapper;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.ai.cutover.module.system.model.entity.table.SysUserRoleTableDef.SYS_USER_ROLE;

/**
 * 系统角色服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysRoleServiceImpl implements SysRoleService {

	private final SysRoleDao sysRoleDao;

	private final SysUserRoleDao sysUserRoleDao;

	private final SysRolePermissionDao sysRolePermissionDao;

	private final Converter converter;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public SysRoleDTO createRole(CreateRoleReq request) {
		// 1. 参数校验
		validateCreateRoleRequest(request);

		// 2. 检查角色编码和名称是否已存在
		checkRoleUniqueness(request.getRoleCode(), request.getRoleName(), null);

		// 3. 构建角色实体
		SysRole role = buildRoleFromCreateRequest(request);

		// 4. 保存角色
		sysRoleDao.insert(role);

		// 5. 分配权限
		if (CollUtil.isNotEmpty(request.getPermissionIds())) {
			assignRolePermissions(role.getId(), request.getPermissionIds());
		}

		// 6. 返回角色DTO
		return getRoleById(role.getId());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public SysRoleDTO updateRole(UpdateRoleReq request) {
		// 1. 检查角色是否存在
		SysRole existingRole = sysRoleDao.selectOneById(request.getId());
		Assert.notNull(existingRole, ErrorMessages.Role.ROLE_NOT_FOUND);

		// 2. 检查是否为内置角色
		Assert.isFalse(BusinessConstants.YesNo.YES.equals(existingRole.getBuiltinFlag()),
				ErrorMessages.Role.CANNOT_MODIFY_BUILTIN_ROLE);

		// 3. 检查角色名称唯一性
		checkRoleUniqueness(null, request.getRoleName(), request.getId());

		// 4. 更新角色信息
		SysRole updateRole = converter.convert(request, SysRole.class);
		sysRoleDao.update(updateRole);

		// 5. 更新角色权限
		if (request.getPermissionIds() != null) {
			assignRolePermissions(request.getId(), request.getPermissionIds());
		}

		return getRoleById(request.getId());
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void deleteRole(Long roleId) {
		SysRole role = sysRoleDao.selectOneById(roleId);
		Assert.notNull(role, ErrorMessages.Role.ROLE_NOT_FOUND);
		Assert.isFalse(BusinessConstants.YesNo.YES.equals(role.getBuiltinFlag()),
				ErrorMessages.Role.CANNOT_DELETE_BUILTIN_ROLE, role.getRoleName());

		// 检查是否有用户关联
		long userCount = QueryChain.of(sysUserRoleDao).where(SysUserRole::getRoleId).eq(roleId).count();
		Assert.isTrue(userCount == 0, ErrorMessages.Role.ROLE_HAS_USERS, role.getRoleName(), userCount);

		// 删除角色权限关联
		QueryWrapper deleteWrapper = QueryWrapper.create().where(SysRolePermission::getRoleId).eq(roleId);
		sysRolePermissionDao.deleteByQuery(deleteWrapper);

		// 删除角色
		sysRoleDao.deleteById(roleId);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void batchDeleteRoles(List<Long> roleIds) {
		if (CollUtil.isEmpty(roleIds)) {
			return;
		}

		// 检查是否包含内置角色
		List<SysRole> roles = sysRoleDao.selectListByIds(roleIds);
		boolean hasBuiltinRole = roles.stream()
			.anyMatch(role -> BusinessConstants.YesNo.YES.equals(role.getBuiltinFlag()));
		Assert.isFalse(hasBuiltinRole, ErrorMessages.Common.CANNOT_DELETE_BUILTIN);

		// 检查是否有用户关联 - 批量查询优化
		List<Long> roleIdsWithUsers = QueryChain.of(sysUserRoleDao)
			.select(SYS_USER_ROLE.ROLE_ID)
			.where(SysUserRole::getRoleId)
			.in(roleIds)
			.listAs(Long.class);
		Assert.isTrue(CollUtil.isEmpty(roleIdsWithUsers), ErrorMessages.Role.ROLE_HAS_USERS + ": " + roleIdsWithUsers);

		// 删除角色权限关联
		QueryWrapper deleteWrapper = QueryWrapper.create().where(SysRolePermission::getRoleId).in(roleIds);
		sysRolePermissionDao.deleteByQuery(deleteWrapper);

		// 批量删除角色
		sysRoleDao.deleteBatchByIds(roleIds);
	}

	@Override
	public SysRoleDTO getRoleById(Long roleId) {
		SysRole role = sysRoleDao.selectOneById(roleId);
		if (role == null) {
			return null;
		}

		SysRoleDTO roleDTO = converter.convert(role, SysRoleDTO.class);

		// 设置用户数量
		long userCount = QueryChain.of(sysUserRoleDao).where(SysUserRole::getRoleId).eq(roleId).count();
		roleDTO.setUserCount(Math.toIntExact(userCount));

		// 设置权限ID列表
		List<Long> permissionIds = QueryChain.of(sysRolePermissionDao)
			.where(SysRolePermission::getRoleId)
			.eq(roleId)
			.list()
			.stream()
			.map(SysRolePermission::getPermissionId)
			.collect(Collectors.toList());
		roleDTO.setPermissionIds(permissionIds);

		return roleDTO;
	}

	@Override
	public SysRoleDTO getRoleByCode(String roleCode) {
		SysRole role = QueryChain.of(sysRoleDao).where(SysRole::getRoleCode).eq(roleCode).one();
		return role != null ? getRoleById(role.getId()) : null;
	}

	@Override
	public Page<SysRoleDTO> getRolePage(RoleQueryReq request) {
		Page<SysRole> rolePage = QueryChain.of(sysRoleDao)
			.where(SysRole::getRoleCode)
			.like(request.getRoleCode(), StrUtil.isNotBlank(request.getRoleCode()))
			.and(SysRole::getRoleName)
			.like(request.getRoleName(), StrUtil.isNotBlank(request.getRoleName()))
			.and(SysRole::getStatus)
			.eq(request.getStatus(), StrUtil.isNotBlank(request.getStatus()))
			.orderBy(SysRole::getSortOrder, true)
			.orderBy(SysRole::getCreateTime, false)
			.page(new Page<>(request.getPageNum(), request.getPageSize()));

		List<Long> roleIds = rolePage.getRecords().stream().map(SysRole::getId).collect(Collectors.toList());
		Map<Long, Integer> userCountMap = getRoleUserCountMap(roleIds);

		return rolePage.map(role -> {
			SysRoleDTO dto = converter.convert(role, SysRoleDTO.class);
			// 设置用户数量
			Integer userCount = userCountMap.getOrDefault(role.getId(), 0);
			dto.setUserCount(userCount);
			return dto;
		});
	}

	@Override
	public List<SysRoleDTO> getAllRoles() {
		List<SysRole> roleList = QueryChain.of(sysRoleDao)
			.where(SysRole::getStatus)
			.eq(StatusConstants.Role.NORMAL)
			.orderBy(SysRole::getSortOrder, true)
			.list();

		List<SysRoleDTO> roleDTOList = converter.convert(roleList, SysRoleDTO.class);

		// 填充用户数量
		fillRoleUserCounts(roleDTOList);

		return roleDTOList;
	}

	@Override
	public List<SysRoleDTO> getRolesByUserId(Long userId) {
		List<SysRole> roleList = QueryChain.of(sysRoleDao)
			.leftJoin(SysUserRole.class)
			.on(SysRole::getId, SysUserRole::getRoleId)
			.where(SysUserRole::getUserId)
			.eq(userId)
			.and(SysRole::getStatus)
			.eq(StatusConstants.Role.NORMAL)
			.list();

		List<SysRoleDTO> roleDTOList = converter.convert(roleList, SysRoleDTO.class);

		// 填充用户数量
		fillRoleUserCounts(roleDTOList);

		return roleDTOList;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void updateRoleStatus(Long roleId, String status) {
		SysRole role = sysRoleDao.selectOneById(roleId);
		Assert.notNull(role, ErrorMessages.Role.ROLE_NOT_FOUND);

		role.setStatus(status);
		sysRoleDao.update(role);
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public void assignRolePermissions(Long roleId, List<Long> permissionIds) {
		// 删除原有权限关联
		QueryWrapper deleteWrapper = QueryWrapper.create().where(SysRolePermission::getRoleId).eq(roleId);
		sysRolePermissionDao.deleteByQuery(deleteWrapper);

		// 批量添加新的权限关联
		if (CollUtil.isNotEmpty(permissionIds)) {
			List<SysRolePermission> rolePermissions = permissionIds.stream()
				.map(permissionId -> new SysRolePermission(roleId, permissionId))
				.collect(Collectors.toList());
			sysRolePermissionDao.insertBatch(rolePermissions);
		}
	}

	@Override
	public boolean existsByRoleCode(String roleCode, Long excludeId) {
		return QueryChain.of(sysRoleDao)
			.where(SysRole::getRoleCode)
			.eq(roleCode)
			.and(SysRole::getId)
			.ne(excludeId, excludeId != null)
			.exists();
	}

	@Override
	public boolean existsByRoleName(String roleName, Long excludeId) {
		return QueryChain.of(sysRoleDao)
			.where(SysRole::getRoleName)
			.eq(roleName)
			.and(SysRole::getId)
			.ne(excludeId, excludeId != null)
			.exists();
	}

	@Override
	public Integer countUsersByRoleId(Long roleId) {
		long count = QueryChain.of(sysUserRoleDao).where(SysUserRole::getRoleId).eq(roleId).count();
		return Math.toIntExact(count);
	}

	// 私有方法

	private void validateCreateRoleRequest(CreateRoleReq request) {
		Assert.hasText(request.getRoleCode(), ErrorMessages.Role.ROLE_CODE_NOT_BLANK);
		Assert.hasText(request.getRoleName(), ErrorMessages.Role.ROLE_NAME_NOT_BLANK);
	}

	private void checkRoleUniqueness(String roleCode, String roleName, Long excludeId) {
		if (StrUtil.isNotBlank(roleCode)) {
			Assert.uniqueConstraint(existsByRoleCode(roleCode, excludeId), UniqueFieldType.ROLE_CODE, roleCode);
		}
		if (StrUtil.isNotBlank(roleName)) {
			Assert.uniqueConstraint(existsByRoleName(roleName, excludeId), UniqueFieldType.ROLE_NAME, roleName);
		}
	}

	/**
	 * 批量查询角色用户数量映射
	 */
	private Map<Long, Integer> getRoleUserCountMap(List<Long> roleIds) {
		if (CollUtil.isEmpty(roleIds)) {
			return new HashMap<>();
		}

		// 批量查询角色用户关联数据
		List<SysUserRole> userRoles = QueryChain.of(sysUserRoleDao).where(SysUserRole::getRoleId).in(roleIds).list();

		// 按角色ID分组统计用户数量
		return userRoles.stream()
			.collect(Collectors.groupingBy(SysUserRole::getRoleId,
					Collectors.collectingAndThen(Collectors.counting(), Math::toIntExact)));
	}

	private SysRole buildRoleFromCreateRequest(CreateRoleReq request) {
		SysRole role = converter.convert(request, SysRole.class);
		role.setStatus(StrUtil.isNotBlank(request.getStatus()) ? request.getStatus() : StatusConstants.Role.NORMAL);
		role.setDataScope(StrUtil.isNotBlank(request.getDataScope()) ? request.getDataScope()
				: BusinessConstants.DataScope.DEPT_AND_CHILD);
		role.setBuiltinFlag(BusinessConstants.YesNo.NO);
		role.setSortOrder(
				request.getSortOrder() != null ? request.getSortOrder() : BusinessConstants.DefaultValue.DEFAULT_SORT);
		return role;
	}

	/**
	 * 填充角色用户数量
	 */
	private void fillRoleUserCounts(List<SysRoleDTO> roleDTOList) {
		if (CollUtil.isEmpty(roleDTOList)) {
			return;
		}

		List<Long> roleIds = roleDTOList.stream().map(SysRoleDTO::getId).collect(Collectors.toList());

		Map<Long, Integer> userCountMap = getRoleUserCountMap(roleIds);

		roleDTOList.forEach(role -> {
			Integer userCount = userCountMap.getOrDefault(role.getId(), 0);
			role.setUserCount(userCount);
		});
	}

}
