package com.ai.cutover.module.system.model.resp;

import com.ai.cutover.module.system.model.dto.SysUserDTO;
import io.github.linpeilie.annotations.AutoMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;

/**
 * 当前用户信息响应
 *
 * <AUTHOR>
 */
@Data
@Builder
@AutoMapper(target = SysUserDTO.class)
@NoArgsConstructor
@AllArgsConstructor
public class CurrentUserResp {

	/**
	 * 用户ID
	 */
	private Long id;

	/**
	 * 用户名
	 */
	private String username;

	/**
	 * 真实姓名
	 */
	private String realName;

	/**
	 * 昵称
	 */
	private String nickname;

	/**
	 * 头像
	 */
	private String avatar;

	/**
	 * 邮箱
	 */
	private String email;

	/**
	 * 手机号
	 */
	private String phone;

	/**
	 * 性别
	 */
	private String gender;

	/**
	 * 生日
	 */
	private LocalDateTime birthday;

	/**
	 * 部门ID
	 */
	private Long deptId;

	/**
	 * 部门名称
	 */
	private String deptName;

	/**
	 * 职位
	 */
	private String position;

	/**
	 * 状态
	 */
	private String status;

	/**
	 * 角色列表
	 */
	private Set<String> roles;

	/**
	 * 权限列表
	 */
	private Set<String> permissions;

	/**
	 * 菜单列表
	 */
	private List<MenuInfo> menus;

	/**
	 * 最后登录时间
	 */
	private LocalDateTime lastLoginTime;

	/**
	 * 最后登录IP
	 */
	private String lastLoginIp;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 菜单信息
	 */
	@Data
	@Builder
	public static class MenuInfo {

		private Long id;

		private String menuCode;

		private String menuName;

		private String menuType;

		private String path;

		private String component;

		private String icon;

		private Integer sortOrder;

		private Long parentId;

		private List<MenuInfo> children;

	}

}
