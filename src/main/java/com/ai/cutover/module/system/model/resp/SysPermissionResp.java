package com.ai.cutover.module.system.model.resp;

import io.github.linpeilie.annotations.AutoMapper;
import com.ai.cutover.module.system.model.entity.SysPermission;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 系统权限响应类
 *
 * <AUTHOR>
 */
@Data
@AutoMapper(target = SysPermission.class, reverseConvertGenerate = false)
public class SysPermissionResp {

	/**
	 * 权限ID
	 */
	private Long id;

	/**
	 * 权限编码
	 */
	private String permissionCode;

	/**
	 * 权限名称
	 */
	private String permissionName;

	/**
	 * 权限类型（MENU-菜单，BUTTON-按钮，API-接口，DATA-数据）
	 */
	private String permissionType;

	/**
	 * 资源路径
	 */
	private String resourcePath;

	/**
	 * 请求方法（GET、POST、PUT、DELETE等）
	 */
	private String requestMethod;

	/**
	 * 显示顺序
	 */
	private Integer sortOrder;

	/**
	 * 权限状态（NORMAL-正常，DISABLED-禁用）
	 */
	private String status;

	/**
	 * 权限描述
	 */
	private String description;

	/**
	 * 备注
	 */
	private String remark;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 更新时间
	 */
	private LocalDateTime updateTime;

	/**
	 * 创建人
	 */
	private Long createBy;

	/**
	 * 更新人
	 */
	private Long updateBy;

}
