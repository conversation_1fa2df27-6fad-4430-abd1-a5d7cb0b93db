package com.ai.cutover.module.system.constant;

import lombok.experimental.UtilityClass;

/**
 * 系统管理模块常量 定义系统管理模块特有的常量
 *
 * <AUTHOR>
 */
@UtilityClass
public class SystemConstants {

	/**
	 * 数据权限范围
	 */
	@UtilityClass
	public static class DataScope {

		/** 全部数据 */
		public static final String ALL = "ALL";

		/** 本部门及子部门数据 */
		public static final String DEPT_AND_CHILD = "DEPT_AND_CHILD";

		/** 本部门数据 */
		public static final String DEPT_ONLY = "DEPT_ONLY";

		/** 仅本人数据 */
		public static final String SELF_ONLY = "SELF_ONLY";

		/** 自定义数据 */
		public static final String CUSTOM = "CUSTOM";

	}

	/**
	 * 系统管理模块特有的操作类型
	 */
	@UtilityClass
	public static class SystemOperation {

		/** 重置密码 */
		public static final String RESET_PASSWORD = "RESET_PASSWORD";

		/** 修改状态 */
		public static final String CHANGE_STATUS = "CHANGE_STATUS";

		/** 分配权限 */
		public static final String ASSIGN_PERMISSION = "ASSIGN_PERMISSION";

		/** 分配角色 */
		public static final String ASSIGN_ROLE = "ASSIGN_ROLE";

		/** 数据权限设置 */
		public static final String SET_DATA_SCOPE = "SET_DATA_SCOPE";

	}

}
