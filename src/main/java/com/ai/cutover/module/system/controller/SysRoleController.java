package com.ai.cutover.module.system.controller;

import cn.dev33.satoken.annotation.SaCheckPermission;
import com.ai.cutover.module.system.constant.PermissionConstants;
import com.ai.cutover.module.system.model.dto.SysRoleDTO;
import com.ai.cutover.module.system.model.req.CreateRoleReq;
import com.ai.cutover.module.system.model.req.UpdateRoleReq;
import com.ai.cutover.module.system.model.req.RoleQueryReq;
import com.ai.cutover.module.system.service.SysRoleService;
import com.mybatisflex.core.paginate.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 系统角色控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/system/role")
@RequiredArgsConstructor
@Validated
public class SysRoleController {

	private final SysRoleService sysRoleService;

	/**
	 * 创建角色
	 */
	@PostMapping("/create")
	@SaCheckPermission(PermissionConstants.Role.CREATE)
	public SysRoleDTO createRole(@Valid @RequestBody CreateRoleReq request) {
		log.info("创建角色请求: roleCode={}", request.getRoleCode());
		return sysRoleService.createRole(request);
	}

	/**
	 * 更新角色
	 */
	@PostMapping("/update")
	@SaCheckPermission(PermissionConstants.Role.UPDATE)
	public SysRoleDTO updateRole(@Valid @RequestBody UpdateRoleReq request) {
		return sysRoleService.updateRole(request);
	}

	/**
	 * 删除角色
	 */
	@PostMapping("/delete")
	@SaCheckPermission(PermissionConstants.Role.DELETE)
	public void deleteRole(@RequestParam @NotNull Long roleId) {
		sysRoleService.deleteRole(roleId);
	}

	/**
	 * 批量删除角色
	 */
	@PostMapping("/batch-delete")
	@SaCheckPermission(PermissionConstants.Role.DELETE)
	public void batchDeleteRoles(@RequestBody @NotEmpty List<Long> roleIds) {
		sysRoleService.batchDeleteRoles(roleIds);
	}

	/**
	 * 根据ID查询角色详情
	 */
	@GetMapping("/detail")
	@SaCheckPermission(PermissionConstants.Role.VIEW)
	public SysRoleDTO getRoleDetail(@RequestParam @NotNull Long id) {
		return sysRoleService.getRoleById(id);
	}

	/**
	 * 根据角色编码查询角色
	 */
	@GetMapping("/by-code")
	@SaCheckPermission(PermissionConstants.Role.VIEW)
	public SysRoleDTO getRoleByCode(@RequestParam String roleCode) {
		return sysRoleService.getRoleByCode(roleCode);
	}

	/**
	 * 分页查询角色列表
	 */
	@GetMapping("/page")
	@SaCheckPermission(PermissionConstants.Role.VIEW)
	public Page<SysRoleDTO> getRolePage(RoleQueryReq request) {
		return sysRoleService.getRolePage(request);
	}

	/**
	 * 查询所有角色列表
	 */
	@GetMapping("/list")
	public List<SysRoleDTO> getAllRoles() {
		return sysRoleService.getAllRoles();
	}

	/**
	 * 根据用户ID查询角色列表
	 */
	@GetMapping("/by-user")
	public List<SysRoleDTO> getRolesByUserId(@RequestParam @NotNull Long userId) {
		return sysRoleService.getRolesByUserId(userId);
	}

	/**
	 * 更新角色状态
	 */
	@PostMapping("/update-status")
	public void updateRoleStatus(@RequestParam @NotNull Long roleId, @RequestParam String status) {
		sysRoleService.updateRoleStatus(roleId, status);
	}

	/**
	 * 分配角色权限
	 */
	@PostMapping("/assign-permissions")
	public void assignRolePermissions(@RequestParam @NotNull Long roleId, @RequestBody List<Long> permissionIds) {
		sysRoleService.assignRolePermissions(roleId, permissionIds);
	}

	/**
	 * 检查角色编码是否存在
	 */
	@GetMapping("/exists-code")
	public Boolean existsByRoleCode(@RequestParam String roleCode, @RequestParam(required = false) Long excludeId) {
		return sysRoleService.existsByRoleCode(roleCode, excludeId);
	}

	/**
	 * 检查角色名称是否存在
	 */
	@GetMapping("/exists-name")
	public Boolean existsByRoleName(@RequestParam String roleName, @RequestParam(required = false) Long excludeId) {
		return sysRoleService.existsByRoleName(roleName, excludeId);
	}

	/**
	 * 统计角色下的用户数量
	 */
	@GetMapping("/user-count")
	public Integer countUsersByRoleId(@RequestParam @NotNull Long roleId) {
		return sysRoleService.countUsersByRoleId(roleId);
	}

}
