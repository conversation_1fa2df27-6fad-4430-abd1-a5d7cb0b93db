package com.ai.cutover.module.system.service;

import com.ai.cutover.module.system.model.dto.SysPermissionDTO;
import com.mybatisflex.core.paginate.Page;

import java.util.List;

/**
 * 系统权限服务接口
 *
 * <AUTHOR>
 */
public interface SysPermissionService {

	/**
	 * 根据ID查询权限
	 * @param permissionId 权限ID
	 * @return 权限DTO
	 */
	SysPermissionDTO getPermissionById(Long permissionId);

	/**
	 * 根据权限编码查询权限
	 * @param permissionCode 权限编码
	 * @return 权限DTO
	 */
	SysPermissionDTO getPermissionByCode(String permissionCode);

	/**
	 * 分页查询权限列表
	 * @param pageNum 页码
	 * @param pageSize 页大小
	 * @param permissionCode 权限编码（模糊查询）
	 * @param permissionName 权限名称（模糊查询）
	 * @param permissionType 权限类型
	 * @param status 权限状态
	 * @return 权限分页列表
	 */
	Page<SysPermissionDTO> getPermissionPage(Integer pageNum, Integer pageSize, String permissionCode,
			String permissionName, String permissionType, String status);

	/**
	 * 根据角色ID查询权限列表
	 * @param roleId 角色ID
	 * @return 权限列表
	 */
	List<SysPermissionDTO> getPermissionsByRoleId(Long roleId);

	/**
	 * 根据用户ID查询权限列表
	 * @param userId 用户ID
	 * @return 权限列表
	 */
	List<SysPermissionDTO> getPermissionsByUserId(Long userId);

	/**
	 * 查询所有正常状态的权限
	 * @return 权限列表
	 */
	List<SysPermissionDTO> getAllPermissions();

	/**
	 * 根据权限类型查询权限列表
	 * @param permissionType 权限类型
	 * @return 权限列表
	 */
	List<SysPermissionDTO> getPermissionsByType(String permissionType);

	/**
	 * 检查权限编码是否存在
	 * @param permissionCode 权限编码
	 * @param excludeId 排除的权限ID
	 * @return 是否存在
	 */
	boolean existsByPermissionCode(String permissionCode, Long excludeId);

	/**
	 * 检查权限名称是否存在
	 * @param permissionName 权限名称
	 * @param excludeId 排除的权限ID
	 * @return 是否存在
	 */
	boolean existsByPermissionName(String permissionName, Long excludeId);

}
