package com.ai.cutover.module.system.model.req;

import com.ai.cutover.common.constant.BusinessConstants;
import lombok.Data;

import jakarta.validation.constraints.*;
import java.util.List;

/**
 * 创建用户请求
 *
 * <AUTHOR>
 */
@Data
public class CreateUserReq {

	/**
	 * 用户名
	 */
	@NotBlank(message = "用户名不能为空")
	@Size(min = 4, max = 20, message = "用户名长度必须在4-20个字符之间")
	@Pattern(regexp = BusinessConstants.Regex.USERNAME, message = "用户名只能包含字母、数字和下划线")
	private String username;

	/**
	 * 密码
	 */
	@NotBlank(message = "密码不能为空")
	@Size(min = 6, max = 20, message = "密码长度必须在6-20个字符之间")
	private String password;

	/**
	 * 确认密码
	 */
	@NotBlank(message = "确认密码不能为空")
	private String confirmPassword;

	/**
	 * 真实姓名
	 */
	@NotBlank(message = "真实姓名不能为空")
	@Size(max = 50, message = "真实姓名长度不能超过50个字符")
	private String realName;

	/**
	 * 昵称
	 */
	@Size(max = 50, message = "昵称长度不能超过50个字符")
	private String nickname;

	/**
	 * 邮箱
	 */
	@Email(message = "邮箱格式不正确")
	@Size(max = 100, message = "邮箱长度不能超过100个字符")
	private String email;

	/**
	 * 手机号
	 */
	@Pattern(regexp = BusinessConstants.Regex.PHONE, message = "手机号格式不正确")
	private String phone;

	/**
	 * 性别
	 */
	private String gender;

	/**
	 * 头像地址
	 */
	private String avatar;

	/**
	 * 部门ID
	 */
	@NotNull(message = "部门不能为空")
	private Long deptId;

	/**
	 * 职位
	 */
	@Size(max = 100, message = "职位长度不能超过100个字符")
	private String position;

	/**
	 * 用户状态
	 */
	private String status;

	/**
	 * 角色ID列表
	 */
	private List<Long> roleIds;

	/**
	 * 备注
	 */
	@Size(max = 500, message = "备注长度不能超过500个字符")
	private String remark;

}
