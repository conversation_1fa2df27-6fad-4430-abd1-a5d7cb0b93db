package com.ai.cutover.module.system.service;

import com.ai.cutover.module.system.model.dto.SysDeptDTO;
import com.ai.cutover.module.system.model.req.CreateDeptReq;
import com.ai.cutover.module.system.model.req.UpdateDeptReq;
import com.ai.cutover.module.system.model.req.DeptQueryReq;

import java.util.List;

/**
 * 系统部门服务接口
 *
 * <AUTHOR>
 */
public interface SysDeptService {

	/**
	 * 创建部门
	 * @param request 创建部门请求
	 * @return 部门DTO
	 */
	SysDeptDTO createDept(CreateDeptReq request);

	/**
	 * 更新部门
	 * @param request 更新部门请求
	 * @return 部门DTO
	 */
	SysDeptDTO updateDept(UpdateDeptReq request);

	/**
	 * 删除部门
	 * @param deptId 部门ID
	 */
	void deleteDept(Long deptId);

	/**
	 * 根据ID查询部门
	 * @param deptId 部门ID
	 * @return 部门DTO
	 */
	SysDeptDTO getDeptById(Long deptId);

	/**
	 * 根据部门编码查询部门
	 * @param deptCode 部门编码
	 * @return 部门DTO
	 */
	SysDeptDTO getDeptByCode(String deptCode);

	/**
	 * 查询部门树形列表
	 * @param request 查询请求
	 * @return 部门树形列表
	 */
	List<SysDeptDTO> getDeptTree(DeptQueryReq request);

	/**
	 * 根据父部门ID查询子部门列表
	 * @param parentId 父部门ID
	 * @return 子部门列表
	 */
	List<SysDeptDTO> getDeptsByParentId(Long parentId);

	/**
	 * 查询部门的所有子部门ID（包括自身）
	 * @param deptId 部门ID
	 * @return 部门ID列表
	 */
	List<Long> getChildrenIds(Long deptId);

	/**
	 * 查询部门的所有父部门ID（不包括自身）
	 * @param deptId 部门ID
	 * @return 部门ID列表
	 */
	List<Long> getParentIds(Long deptId);

	/**
	 * 更新部门状态
	 * @param deptId 部门ID
	 * @param status 状态
	 */
	void updateDeptStatus(Long deptId, String status);

	/**
	 * 检查部门编码是否存在
	 * @param deptCode 部门编码
	 * @param excludeId 排除的部门ID
	 * @return 是否存在
	 */
	boolean existsByDeptCode(String deptCode, Long excludeId);

	/**
	 * 检查部门名称是否存在（同级）
	 * @param deptName 部门名称
	 * @param parentId 父部门ID
	 * @param excludeId 排除的部门ID
	 * @return 是否存在
	 */
	boolean existsByDeptName(String deptName, Long parentId, Long excludeId);

	/**
	 * 检查部门是否有子部门
	 * @param deptId 部门ID
	 * @return 是否有子部门
	 */
	boolean hasChildren(Long deptId);

	/**
	 * 统计部门下的用户数量
	 * @param deptId 部门ID
	 * @return 用户数量
	 */
	Integer countUsersByDeptId(Long deptId);

	/**
	 * 构建部门树形结构
	 * @param deptList 部门列表
	 * @return 部门树形列表
	 */
	List<SysDeptDTO> buildDeptTree(List<SysDeptDTO> deptList);

}
