package com.ai.cutover.module.system.model.req;

import lombok.Data;

/**
 * 用户查询请求
 *
 * <AUTHOR>
 */
@Data
public class UserQueryReq {

	/**
	 * 页码
	 */
	private Integer pageNum = 1;

	/**
	 * 页大小
	 */
	private Integer pageSize = 10;

	/**
	 * 用户名（模糊查询）
	 */
	private String username;

	/**
	 * 真实姓名（模糊查询）
	 */
	private String realName;

	/**
	 * 手机号（模糊查询）
	 */
	private String phone;

	/**
	 * 邮箱（模糊查询）
	 */
	private String email;

	/**
	 * 用户状态
	 */
	private String status;

	/**
	 * 部门ID
	 */
	private Long deptId;

	/**
	 * 开始时间
	 */
	private String startTime;

	/**
	 * 结束时间
	 */
	private String endTime;

}
