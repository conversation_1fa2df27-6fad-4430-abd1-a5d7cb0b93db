package com.ai.cutover.module.system.service;

import com.ai.cutover.module.system.model.dto.SysUserDTO;
import com.ai.cutover.module.system.model.entity.SysUser;
import com.ai.cutover.module.system.model.req.CreateUserReq;
import com.ai.cutover.module.system.model.req.UpdateUserReq;
import com.ai.cutover.module.system.model.req.UserQueryReq;
import com.mybatisflex.core.paginate.Page;

import java.util.List;
import java.util.Set;

/**
 * 系统用户服务接口
 *
 * <AUTHOR>
 */
public interface SysUserService {

	/**
	 * 创建用户
	 * @param request 创建用户请求
	 * @return 用户DTO
	 */
	SysUserDTO createUser(CreateUserReq request);

	/**
	 * 更新用户
	 * @param request 更新用户请求
	 * @return 用户DTO
	 */
	SysUserDTO updateUser(UpdateUserReq request);

	/**
	 * 删除用户
	 * @param userId 用户ID
	 */
	void deleteUser(Long userId);

	/**
	 * 批量删除用户
	 * @param userIds 用户ID列表
	 */
	void batchDeleteUsers(List<Long> userIds);

	/**
	 * 根据ID查询用户
	 * @param userId 用户ID
	 * @return 用户DTO
	 */
	SysUserDTO getUserById(Long userId);

	/**
	 * 根据ID查询用户实体
	 * @param userId 用户ID
	 * @return 用户实体
	 */
	SysUser getUserEntityById(Long userId);

	/**
	 * 根据用户名查询用户
	 * @param username 用户名
	 * @return 用户DTO
	 */
	SysUserDTO getUserByUsername(String username);

	/**
	 * 分页查询用户列表
	 * @param request 查询请求
	 * @return 用户分页列表
	 */
	Page<SysUserDTO> getUserPage(UserQueryReq request);

	/**
	 * 查询所有用户列表
	 * @return 用户列表
	 */
	List<SysUserDTO> getAllUsers();

	/**
	 * 根据部门ID查询用户列表
	 * @param deptId 部门ID
	 * @return 用户列表
	 */
	List<SysUserDTO> getUsersByDeptId(Long deptId);

	/**
	 * 根据角色ID查询用户列表
	 * @param roleId 角色ID
	 * @return 用户列表
	 */
	List<SysUserDTO> getUsersByRoleId(Long roleId);

	/**
	 * 重置用户密码
	 * @param userId 用户ID
	 * @param newPassword 新密码
	 */
	void resetPassword(Long userId, String newPassword);

	/**
	 * 修改用户密码
	 * @param userId 用户ID
	 * @param oldPassword 原密码
	 * @param newPassword 新密码
	 */
	void changePassword(Long userId, String oldPassword, String newPassword);

	/**
	 * 更新用户状态
	 * @param userId 用户ID
	 * @param status 状态
	 */
	void updateUserStatus(Long userId, String status);

	/**
	 * 分配用户角色
	 * @param userId 用户ID
	 * @param roleIds 角色ID列表
	 */
	void assignUserRoles(Long userId, List<Long> roleIds);

	/**
	 * 查询用户权限
	 * @param userId 用户ID
	 * @return 权限标识集合
	 */
	Set<String> getUserPermissions(Long userId);

	/**
	 * 查询用户角色
	 * @param userId 用户ID
	 * @return 角色编码集合
	 */
	Set<String> getUserRoles(Long userId);

	/**
	 * 检查用户名是否存在
	 * @param username 用户名
	 * @param excludeId 排除的用户ID
	 * @return 是否存在
	 */
	boolean existsByUsername(String username, Long excludeId);

	/**
	 * 检查邮箱是否存在
	 * @param email 邮箱
	 * @param excludeId 排除的用户ID
	 * @return 是否存在
	 */
	boolean existsByEmail(String email, Long excludeId);

	/**
	 * 检查手机号是否存在
	 * @param phone 手机号
	 * @param excludeId 排除的用户ID
	 * @return 是否存在
	 */
	boolean existsByPhone(String phone, Long excludeId);

	/**
	 * 更新用户最后登录信息
	 * @param userId 用户ID
	 * @param loginIp 登录IP
	 */
	void updateLastLoginInfo(Long userId, String loginIp);

	/**
	 * 锁定用户
	 * @param userId 用户ID
	 */
	void lockUser(Long userId);

	/**
	 * 解锁用户
	 * @param userId 用户ID
	 */
	void unlockUser(Long userId);

	/**
	 * 验证密码
	 * @param rawPassword 原始密码
	 * @param encodedPassword 加密密码
	 * @return 是否匹配
	 */
	boolean verifyPassword(String rawPassword, String encodedPassword);

	/**
	 * 加密密码
	 * @param rawPassword 原始密码
	 * @return 加密密码
	 */
	String encodePassword(String rawPassword);

}
