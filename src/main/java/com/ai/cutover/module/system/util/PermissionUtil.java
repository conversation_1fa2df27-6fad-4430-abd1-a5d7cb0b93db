package com.ai.cutover.module.system.util;

import cn.dev33.satoken.stp.StpUtil;
import com.ai.cutover.module.system.constant.RoleConstants;

/**
 * 权限工具类 提供常用的权限检查方法
 *
 * <AUTHOR>
 */
public final class PermissionUtil {

	private PermissionUtil() {
	}

	/**
	 * 检查当前用户是否为超级管理员
	 * @return 是否为超级管理员
	 */
	public static boolean isSuperAdmin() {
		if (!StpUtil.isLogin()) {
			return false;
		}
		return StpUtil.hasRole(RoleConstants.SUPER_ADMIN);
	}

	/**
	 * 检查当前用户是否为管理员（包括超级管理员和系统管理员）
	 * @return 是否为管理员
	 */
	public static boolean isAdmin() {
		if (!StpUtil.isLogin()) {
			return false;
		}
		return StpUtil.hasRoleOr(RoleConstants.SUPER_ADMIN, RoleConstants.ADMIN);
	}

	/**
	 * 检查当前用户是否为部门管理员
	 * @return 是否为部门管理员
	 */
	public static boolean isDeptAdmin() {
		if (!StpUtil.isLogin()) {
			return false;
		}
		return StpUtil.hasRole(RoleConstants.DEPT_ADMIN);
	}

	/**
	 * 检查当前用户是否有指定权限
	 * @param permission 权限标识
	 * @return 是否有权限
	 */
	public static boolean hasPermission(String permission) {
		if (!StpUtil.isLogin()) {
			return false;
		}
		// 超级管理员拥有所有权限
		if (isSuperAdmin()) {
			return true;
		}
		return StpUtil.hasPermission(permission);
	}

	/**
	 * 检查当前用户是否有指定角色
	 * @param role 角色标识
	 * @return 是否有角色
	 */
	public static boolean hasRole(String role) {
		if (!StpUtil.isLogin()) {
			return false;
		}
		return StpUtil.hasRole(role);
	}

	/**
	 * 检查当前用户是否有任意一个指定权限
	 * @param permissions 权限标识数组
	 * @return 是否有任意一个权限
	 */
	public static boolean hasAnyPermission(String... permissions) {
		if (!StpUtil.isLogin()) {
			return false;
		}
		// 超级管理员拥有所有权限
		if (isSuperAdmin()) {
			return true;
		}
		for (String permission : permissions) {
			if (StpUtil.hasPermission(permission)) {
				return true;
			}
		}
		return false;
	}

	/**
	 * 检查当前用户是否有任意一个指定角色
	 * @param roles 角色标识数组
	 * @return 是否有任意一个角色
	 */
	public static boolean hasAnyRole(String... roles) {
		if (!StpUtil.isLogin()) {
			return false;
		}
		return StpUtil.hasRoleOr(roles);
	}

	/**
	 * 检查当前用户是否拥有所有指定权限
	 * @param permissions 权限标识数组
	 * @return 是否拥有所有权限
	 */
	public static boolean hasAllPermissions(String... permissions) {
		if (!StpUtil.isLogin()) {
			return false;
		}
		// 超级管理员拥有所有权限
		if (isSuperAdmin()) {
			return true;
		}
		for (String permission : permissions) {
			if (!StpUtil.hasPermission(permission)) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 检查当前用户是否拥有所有指定角色
	 * @param roles 角色标识数组
	 * @return 是否拥有所有角色
	 */
	public static boolean hasAllRoles(String... roles) {
		if (!StpUtil.isLogin()) {
			return false;
		}
		return StpUtil.hasRoleAnd(roles);
	}

	/**
	 * 获取当前登录用户ID
	 * @return 用户ID
	 */
	public static Long getCurrentUserId() {
		if (!StpUtil.isLogin()) {
			return null;
		}
		return StpUtil.getLoginIdAsLong();
	}

	/**
	 * 检查是否为当前用户本人或管理员
	 * @param userId 要检查的用户ID
	 * @return 是否为本人或管理员
	 */
	public static boolean isSelfOrAdmin(Long userId) {
		if (!StpUtil.isLogin()) {
			return false;
		}
		Long currentUserId = getCurrentUserId();
		return currentUserId.equals(userId) || isAdmin();
	}

}
