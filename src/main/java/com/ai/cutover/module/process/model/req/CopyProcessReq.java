package com.ai.cutover.module.process.model.req;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 复制流程定义请求
 *
 * <AUTHOR>
 */
@Data
public class CopyProcessReq {

	/**
	 * 源流程Key
	 */
	@NotBlank(message = "源流程Key不能为空")
	private String sourceProcessKey;

	/**
	 * 新流程Key
	 */
	@NotBlank(message = "新流程Key不能为空")
	private String newProcessKey;

	/**
	 * 新流程名称
	 */
	@NotBlank(message = "新流程名称不能为空")
	private String newProcessName;

}
