package com.ai.cutover.module.process.model.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 任务信息响应
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskInfoResp {

	/**
	 * 任务ID
	 */
	private String taskId;

	/**
	 * 任务名称
	 */
	private String taskName;

	/**
	 * 任务描述
	 */
	private String description;

	/**
	 * 处理人
	 */
	private String assignee;

	/**
	 * 候选人
	 */
	private String candidateUsers;

	/**
	 * 候选组
	 */
	private String candidateGroups;

	/**
	 * 创建时间
	 */
	private String createTime;

	/**
	 * 到期时间
	 */
	private String dueDate;

	/**
	 * 优先级
	 */
	private Integer priority;

	/**
	 * 流程实例ID
	 */
	private String processInstanceId;

	/**
	 * 流程定义Key
	 */
	private String processDefinitionKey;

	/**
	 * 任务变量
	 */
	private Map<String, Object> variables;

}
