package com.ai.cutover.module.process.listener;

import com.ai.cutover.common.constant.CommonConstants;
import com.ai.cutover.module.process.constant.ProcessConstants;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.RepositoryService;
import org.camunda.bpm.engine.delegate.DelegateTask;
import org.camunda.bpm.engine.delegate.TaskListener;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * 流程任务监听器 集成流程事件处理器和审批人提供者
 *
 * <AUTHOR>
 */
@Slf4j
@Component("processTaskListener")
public class ProcessTaskListener implements TaskListener {

	private final List<ProcessEventHandler> eventHandlers;

	private final List<ApproverProvider> approverProviders;

	private final RepositoryService repositoryService;

	public ProcessTaskListener(@Autowired(required = false) List<ProcessEventHandler> eventHandlers,
			@Autowired(required = false) List<ApproverProvider> approverProviders,
			RepositoryService repositoryService) {
		this.eventHandlers = eventHandlers;
		this.approverProviders = approverProviders;
		this.repositoryService = repositoryService;
	}

	@Override
	public void notify(DelegateTask delegateTask) {
		String eventName = delegateTask.getEventName();
		String taskId = delegateTask.getId();
		String taskKey = delegateTask.getTaskDefinitionKey();
		String taskName = delegateTask.getName();
		String processInstanceId = delegateTask.getProcessInstanceId();

		log.debug("处理任务事件: eventName={}, taskId={}, taskKey={}, processInstanceId={}", eventName, taskId, taskKey,
				processInstanceId);

		try {
			switch (eventName) {
				case ProcessConstants.TaskEventType.CREATE:
					handleTaskCreate(delegateTask);
					break;
				case ProcessConstants.TaskEventType.ASSIGNMENT:
					handleTaskAssignment(delegateTask);
					break;
				case ProcessConstants.TaskEventType.COMPLETE:
					handleTaskComplete(delegateTask);
					break;
				case ProcessConstants.TaskEventType.DELETE:
					handleTaskDelete(delegateTask);
					break;
				default:
					log.debug("未处理的任务事件类型: {}", eventName);
			}
		}
		catch (Exception e) {
			log.error("处理任务事件失败: eventName={}, taskId={}, error={}", eventName, taskId, e.getMessage(), e);
		}
	}

	/**
	 * 处理任务创建事件
	 */
	private void handleTaskCreate(DelegateTask delegateTask) {
		String taskId = delegateTask.getId();
		String taskKey = delegateTask.getTaskDefinitionKey();
		String taskName = delegateTask.getName();
		String processInstanceId = delegateTask.getProcessInstanceId();

		log.info("任务创建: taskId={}, taskKey={}, taskName={}, processInstanceId={}", taskId, taskKey, taskName,
				processInstanceId);

		// 设置审批人
		setTaskApprovers(delegateTask);

		// 触发事件处理器
		triggerTaskCreatedEvent(taskId, taskKey, taskName, delegateTask.getAssignee(), processInstanceId);
	}

	/**
	 * 处理任务分配事件
	 */
	private void handleTaskAssignment(DelegateTask delegateTask) {
		String taskId = delegateTask.getId();
		String assignee = delegateTask.getAssignee();
		String owner = delegateTask.getOwner();

		log.info("任务分配: taskId={}, assignee={}, owner={}", taskId, assignee, owner);

		// 触发事件处理器
		triggerTaskAssignedEvent(taskId, owner, assignee);
	}

	/**
	 * 处理任务完成事件
	 */
	private void handleTaskComplete(DelegateTask delegateTask) {
		String taskId = delegateTask.getId();
		String taskKey = delegateTask.getTaskDefinitionKey();
		String taskName = delegateTask.getName();
		String processInstanceId = delegateTask.getProcessInstanceId();
		Map<String, Object> variables = delegateTask.getVariables();

		// 获取当前用户（实际项目中应该从安全上下文获取）
		String userId = getCurrentUserId(delegateTask);

		log.info("任务完成: taskId={}, taskKey={}, taskName={}, userId={}, processInstanceId={}", taskId, taskKey, taskName,
				userId, processInstanceId);

		// 检查是否是审批任务
		if (isApprovalTask(variables)) {
			handleApprovalTask(taskId, taskKey, variables, userId, processInstanceId);
		}

		// 触发事件处理器
		triggerTaskCompletedEvent(taskId, taskKey, taskName, userId, processInstanceId, variables);
	}

	/**
	 * 处理任务删除事件
	 */
	private void handleTaskDelete(DelegateTask delegateTask) {
		String taskId = delegateTask.getId();
		String deleteReason = delegateTask.getDeleteReason();

		log.info("任务删除: taskId={}, deleteReason={}", taskId, deleteReason);
	}

	/**
	 * 设置任务审批人
	 */
	private void setTaskApprovers(DelegateTask delegateTask) {
		if (approverProviders == null || approverProviders.isEmpty()) {
			log.debug("未找到审批人提供者");
			return;
		}

		String processKey = getProcessKey(delegateTask);
		String taskKey = delegateTask.getTaskDefinitionKey();
		String processInstanceId = delegateTask.getProcessInstanceId();
		Map<String, Object> variables = delegateTask.getVariables();

		List<String> approvers = approverProviders.stream()
			.filter(provider -> provider.supports(processKey, taskKey))
			.sorted(Comparator.comparingInt(ApproverProvider::getOrder))
			.findFirst()
			.map(provider -> provider.getApprovers(processKey, taskKey, processInstanceId, variables))
			.orElse(null);

		if (approvers != null && !approvers.isEmpty()) {
			if (approvers.size() == CommonConstants.NumberConstants.ONE) {
				// 单个审批人直接分配
				delegateTask.setAssignee(approvers.get(CommonConstants.NumberConstants.ZERO));
				log.info("设置任务审批人: taskId={}, assignee={}", delegateTask.getId(),
						approvers.get(CommonConstants.NumberConstants.ZERO));
			}
			else {
				// 多个审批人设置为候选人
				delegateTask.addCandidateUsers(approvers);
				log.info("设置任务候选人: taskId={}, candidates={}", delegateTask.getId(), approvers);
			}
		}
		else {
			log.warn("未找到合适的审批人: taskKey={}, processKey={}", taskKey, processKey);
		}
	}

	/**
	 * 获取流程定义Key
	 */
	private String getProcessKey(DelegateTask delegateTask) {
		try {
			String processDefinitionId = delegateTask.getProcessDefinitionId();
			ProcessDefinition processDefinition = repositoryService.getProcessDefinition(processDefinitionId);
			return processDefinition.getKey();
		}
		catch (Exception e) {
			log.warn("获取流程定义Key失败: processDefinitionId={}", delegateTask.getProcessDefinitionId(), e);
			return null;
		}
	}

	/**
	 * 获取当前用户ID
	 */
	private String getCurrentUserId(DelegateTask delegateTask) {
		// 实际项目中应该从安全上下文或其他地方获取当前用户
		String assignee = delegateTask.getAssignee();
		if (assignee != null) {
			return assignee;
		}

		// 从变量中获取
		Object userId = delegateTask.getVariable(ProcessConstants.VariableKey.CURRENT_USER_ID);
		if (userId != null) {
			return userId.toString();
		}

		return CommonConstants.SystemConstants.SYSTEM_USER;
	}

	/**
	 * 判断是否是审批任务
	 */
	private boolean isApprovalTask(Map<String, Object> variables) {
		return variables != null && variables.containsKey(ProcessConstants.VariableKey.APPROVED);
	}

	/**
	 * 处理审批任务
	 */
	private void handleApprovalTask(String taskId, String taskKey, Map<String, Object> variables, String userId,
			String processInstanceId) {
		Object approvedObj = variables.get(ProcessConstants.VariableKey.APPROVED);
		Object commentObj = variables.get(ProcessConstants.VariableKey.COMMENT);

		if (approvedObj instanceof Boolean) {
			boolean approved = (Boolean) approvedObj;
			String comment = commentObj != null ? commentObj.toString() : CommonConstants.StringConstants.EMPTY;

			log.info("处理审批任务: taskId={}, approved={}, comment={}", taskId, approved, comment);

			// 触发审批事件
			triggerTaskApprovedEvent(taskId, taskKey, approved, comment, userId, processInstanceId);
		}
	}

	/**
	 * 触发任务创建事件
	 */
	private void triggerTaskCreatedEvent(String taskId, String taskKey, String taskName, String assigneeId,
			String processInstanceId) {
		if (eventHandlers == null) {
			return;
		}

		eventHandlers.stream()
			.filter(handler -> handler.supports(null, taskKey, processInstanceId, null))
			.sorted(Comparator.comparingInt(ProcessEventHandler::getOrder))
			.forEach(handler -> {
				try {
					handler.onTaskCreated(taskId, taskKey, taskName, assigneeId, processInstanceId);
				}
				catch (Exception e) {
					log.error("任务创建事件处理失败: handler={}, taskId={}", handler.getName(), taskId, e);
				}
			});
	}

	/**
	 * 触发任务分配事件
	 */
	private void triggerTaskAssignedEvent(String taskId, String fromUserId, String toUserId) {
		if (eventHandlers == null) {
			return;
		}

		eventHandlers.stream().sorted(Comparator.comparingInt(ProcessEventHandler::getOrder)).forEach(handler -> {
			try {
				handler.onTaskAssigned(taskId, fromUserId, toUserId);
			}
			catch (Exception e) {
				log.error("任务分配事件处理失败: handler={}, taskId={}", handler.getName(), taskId, e);
			}
		});
	}

	/**
	 * 触发任务完成事件
	 */
	private void triggerTaskCompletedEvent(String taskId, String taskKey, String taskName, String userId,
			String processInstanceId, Map<String, Object> variables) {
		if (eventHandlers == null) {
			return;
		}

		eventHandlers.stream()
			.filter(handler -> handler.supports(null, taskKey, processInstanceId, variables))
			.sorted(Comparator.comparingInt(ProcessEventHandler::getOrder))
			.forEach(handler -> {
				try {
					handler.onTaskCompleted(taskId, taskKey, taskName, userId, processInstanceId, variables);
				}
				catch (Exception e) {
					log.error("任务完成事件处理失败: handler={}, taskId={}", handler.getName(), taskId, e);
				}
			});
	}

	/**
	 * 触发任务审批事件
	 */
	private void triggerTaskApprovedEvent(String taskId, String taskKey, boolean approved, String comment,
			String userId, String processInstanceId) {
		if (eventHandlers == null) {
			return;
		}

		eventHandlers.stream().sorted(Comparator.comparingInt(ProcessEventHandler::getOrder)).forEach(handler -> {
			try {
				handler.onTaskApproved(taskId, taskKey, approved, comment, userId, processInstanceId);
			}
			catch (Exception e) {
				log.error("任务审批事件处理失败: handler={}, taskId={}", handler.getName(), taskId, e);
			}
		});
	}

}
