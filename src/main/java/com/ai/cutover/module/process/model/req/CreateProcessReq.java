package com.ai.cutover.module.process.model.req;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;

/**
 * 创建流程定义请求
 *
 * <AUTHOR>
 */
@Data
public class CreateProcessReq {

	/**
	 * 流程定义Key
	 */
	@NotBlank(message = "流程Key不能为空")
	private String processKey;

	/**
	 * 流程名称
	 */
	@NotBlank(message = "流程名称不能为空")
	private String processName;

	/**
	 * 流程描述
	 */
	private String description;

	/**
	 * 流程分类
	 */
	private String category;

	/**
	 * 设计器类型
	 */
	private String designerType;

	/**
	 * 流程标签
	 */
	private String tags;

	/**
	 * 备注
	 */
	private String remarks;

}
