package com.ai.cutover.module.process.service;

import com.ai.cutover.module.process.model.resp.ProcessHistoryInfoResp;
import com.ai.cutover.module.process.model.resp.TaskInfoResp;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.Task;

import java.util.List;
import java.util.Map;

/**
 * 流程服务接口 定义Camunda流程相关的业务操作
 *
 * <AUTHOR>
 */
public interface ProcessService {

	/**
	 * 启动流程
	 * @param processDefinitionKey 流程定义Key
	 * @param businessKey 业务Key（可选）
	 * @return 流程实例ID
	 */
	String startProcess(String processDefinitionKey, Long businessKey);

	/**
	 * 启动流程（带变量）
	 * @param processDefinitionKey 流程定义Key
	 * @param businessKey 业务Key（可选）
	 * @param variables 流程变量
	 * @return 流程实例ID
	 */
	String startProcess(String processDefinitionKey, Long businessKey, Map<String, Object> variables);

	/**
	 * 完成任务
	 * @param taskId 任务ID
	 * @param variables 任务变量
	 */
	void completeTask(String taskId, Map<String, Object> variables);

	/**
	 * 审批任务（通过/驳回）
	 * @param taskId 任务ID
	 * @param approved 是否通过
	 * @param comment 审批意见
	 * @param userId 审批人ID
	 */
	void approveTask(String taskId, boolean approved, String comment, String userId);

	/**
	 * 查询用户待办任务
	 * @param userId 用户ID
	 * @return 待办任务列表
	 */
	List<Task> getUserTasks(String userId);

	/**
	 * 查询流程实例的所有任务
	 * @param processInstanceId 流程实例ID
	 * @return 任务列表
	 */
	List<Task> getProcessTasks(String processInstanceId);

	/**
	 * 查询流程实例
	 * @param processInstanceId 流程实例ID
	 * @return 流程实例
	 */
	ProcessInstance getProcessInstance(String processInstanceId);

	/**
	 * 查询流程历史
	 * @param processInstanceId 流程实例ID
	 * @return 流程历史信息
	 */
	ProcessHistoryInfoResp getProcessHistory(String processInstanceId);

	/**
	 * 终止流程
	 * @param processInstanceId 流程实例ID
	 * @param reason 终止原因
	 */
	void terminateProcess(String processInstanceId, String reason);

	/**
	 * 挂起流程
	 * @param processInstanceId 流程实例ID
	 */
	void suspendProcess(String processInstanceId);

	/**
	 * 激活流程
	 * @param processInstanceId 流程实例ID
	 */
	void activateProcess(String processInstanceId);

	/**
	 * 查询流程变量
	 * @param processInstanceId 流程实例ID
	 * @return 流程变量
	 */
	Map<String, Object> getProcessVariables(String processInstanceId);

	/**
	 * 设置流程变量
	 * @param processInstanceId 流程实例ID
	 * @param variables 变量
	 */
	void setProcessVariables(String processInstanceId, Map<String, Object> variables);

	/**
	 * 查询任务详情
	 * @param taskId 任务ID
	 * @return 任务信息
	 */
	TaskInfoResp getTaskInfo(String taskId);

	/**
	 * 委派任务
	 * @param taskId 任务ID
	 * @param userId 委派给的用户ID
	 */
	void delegateTask(String taskId, String userId);

	/**
	 * 转办任务
	 * @param taskId 任务ID
	 * @param userId 转办给的用户ID
	 */
	void assignTask(String taskId, String userId);

}
