package com.ai.cutover.module.process.model.dto;

import io.github.linpeilie.annotations.AutoMapper;
import lombok.Data;
import org.camunda.bpm.engine.runtime.ProcessInstance;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 流程实例DTO
 *
 * <AUTHOR>
 */
@Data
public class ProcessInstanceDTO {

	/**
	 * 流程实例ID
	 */
	private String id;

	/**
	 * 业务Key
	 */
	private String businessKey;

	/**
	 * 流程定义ID
	 */
	private String processDefinitionId;

	/**
	 * 流程定义Key
	 */
	private String processDefinitionKey;

	/**
	 * 流程定义名称
	 */
	private String processDefinitionName;

	/**
	 * 流程定义版本
	 */
	private Integer processDefinitionVersion;

	/**
	 * 是否已结束
	 */
	private Boolean ended;

	/**
	 * 是否已挂起
	 */
	private Boolean suspended;

	/**
	 * 租户ID
	 */
	private String tenantId;

	/**
	 * 开始时间
	 */
	private LocalDateTime startTime;

	/**
	 * 结束时间
	 */
	private LocalDateTime endTime;

	/**
	 * 流程变量
	 */
	private Map<String, Object> variables;

}
