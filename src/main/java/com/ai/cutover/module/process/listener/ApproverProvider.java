package com.ai.cutover.module.process.listener;

import java.util.List;
import java.util.Map;

/**
 * 审批人提供者接口 业务方可以实现此接口来动态设置审批人
 *
 * <AUTHOR>
 */
public interface ApproverProvider {

	/**
	 * 获取审批人列表
	 * @param processKey 流程定义Key
	 * @param taskKey 任务Key
	 * @param processInstanceId 流程实例ID
	 * @param variables 流程变量
	 * @return 审批人ID列表
	 */
	List<String> getApprovers(String processKey, String taskKey, String processInstanceId,
			Map<String, Object> variables);

	/**
	 * 是否支持该流程和任务
	 * @param processKey 流程定义Key（可能为null，如果无法获取）
	 * @param taskKey 任务Key
	 * @return 是否支持
	 */
	boolean supports(String processKey, String taskKey);

	/**
	 * 获取提供者优先级 数值越小优先级越高
	 * @return 优先级
	 */
	default int getOrder() {
		return 0;
	}

	/**
	 * 获取提供者名称
	 * @return 提供者名称
	 */
	default String getName() {
		return this.getClass().getSimpleName();
	}

}
