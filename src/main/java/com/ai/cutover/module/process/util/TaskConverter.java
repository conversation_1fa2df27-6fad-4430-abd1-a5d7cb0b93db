package com.ai.cutover.module.process.util;

import com.ai.cutover.module.process.model.dto.TaskDTO;
import org.camunda.bpm.engine.task.Task;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务转换工具类 用于Camunda Task对象与TaskDTO之间的转换
 *
 * <AUTHOR>
 */
@Component
public class TaskConverter {

	/**
	 * 将Camunda Task转换为TaskDTO
	 * @param task Camunda Task对象
	 * @return TaskDTO对象
	 */
	public TaskDTO toTaskDTO(Task task) {
		if (task == null) {
			return null;
		}

		TaskDTO taskDTO = new TaskDTO();
		taskDTO.setId(task.getId());
		taskDTO.setName(task.getName());
		taskDTO.setDescription(task.getDescription());
		taskDTO.setAssignee(task.getAssignee());
		taskDTO.setOwner(task.getOwner());
		taskDTO.setProcessInstanceId(task.getProcessInstanceId());
		taskDTO.setProcessDefinitionId(task.getProcessDefinitionId());
		taskDTO.setTaskDefinitionKey(task.getTaskDefinitionKey());
		taskDTO.setPriority(task.getPriority());

		// 转换时间字段
		if (task.getCreateTime() != null) {
			taskDTO.setCreateTime(convertToLocalDateTime(task.getCreateTime()));
		}
		if (task.getDueDate() != null) {
			taskDTO.setDueDate(convertToLocalDateTime(task.getDueDate()));
		}

		return taskDTO;
	}

	/**
	 * 批量转换Task列表为TaskDTO列表
	 * @param tasks Camunda Task列表
	 * @return TaskDTO列表
	 */
	public List<TaskDTO> toTaskDTOList(List<Task> tasks) {
		if (tasks == null || tasks.isEmpty()) {
			return List.of();
		}

		return tasks.stream().map(this::toTaskDTO).collect(Collectors.toList());
	}

	/**
	 * 将Date转换为LocalDateTime
	 * @param date Date对象
	 * @return LocalDateTime对象
	 */
	private LocalDateTime convertToLocalDateTime(Date date) {
		if (date == null) {
			return null;
		}
		return date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
	}

}
