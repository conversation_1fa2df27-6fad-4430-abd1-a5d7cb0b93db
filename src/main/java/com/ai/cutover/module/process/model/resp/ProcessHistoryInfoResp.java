package com.ai.cutover.module.process.model.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 流程历史信息响应
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessHistoryInfoResp {

	/**
	 * 流程实例ID
	 */
	private String processInstanceId;

	/**
	 * 流程定义Key
	 */
	private String processDefinitionKey;

	/**
	 * 开始时间
	 */
	private String startTime;

	/**
	 * 结束时间
	 */
	private String endTime;

	/**
	 * 持续时间
	 */
	private String duration;

	/**
	 * 活动列表
	 */
	private List<ActivityInfoResp> activities;

}
