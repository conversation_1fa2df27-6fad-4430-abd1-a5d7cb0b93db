package com.ai.cutover.module.process.constant;

import lombok.experimental.UtilityClass;

/**
 * 流程模块常量
 *
 * <AUTHOR>
 */
@UtilityClass
public class ProcessConstants {

	/**
	 * 任务事件类型
	 */
	@UtilityClass
	public static class TaskEventType {

		public static final String CREATE = "create";

		public static final String ASSIGNMENT = "assignment";

		public static final String COMPLETE = "complete";

		public static final String DELETE = "delete";

		public static final String UPDATE = "update";

		public static final String TIMEOUT = "timeout";

	}

	/**
	 * 事件类型
	 */
	@UtilityClass
	public static class EventType {

		public static final String PROCESS_STARTED = "PROCESS_STARTED";

		public static final String PROCESS_COMPLETED = "PROCESS_COMPLETED";

		public static final String PROCESS_TERMINATED = "PROCESS_TERMINATED";

		public static final String TASK_CREATED = "TASK_CREATED";

		public static final String TASK_COMPLETED = "TASK_COMPLETED";

		public static final String TASK_APPROVED = "APPROVED";

		public static final String TASK_REJECTED = "REJECTED";

		public static final String TASK_ASSIGNED = "TASK_ASSIGNED";

		public static final String TASK_DELEGATED = "TASK_DELEGATED";

	}

	/**
	 * 通用流程变量Key
	 */
	@UtilityClass
	public static class VariableKey {

		public static final String INITIATOR = "initiator";

		public static final String APPROVED = "approved";

		public static final String COMMENT = "comment";

		public static final String APPROVAL_COMMENT = "approvalComment";

		public static final String APPROVAL_RESULT = "approvalResult";

		public static final String APPROVER = "approver";

		public static final String APPROVAL_TIME = "approvalTime";

		public static final String CURRENT_USER_ID = "currentUserId";

		public static final String PROCESS_STARTER = "processStarter";

		public static final String BUSINESS_KEY = "businessKey";

	}

	/**
	 * 审批人变量后缀
	 */
	@UtilityClass
	public static class ApproverSuffix {

		public static final String APPROVERS = "_approvers";

		public static final String ROLE = "approverRole";

		public static final String DEPARTMENT = "department";

	}

	/**
	 * 默认审批人
	 */
	@UtilityClass
	public static class DefaultApprover {

		public static final String ADMIN = "admin";

		public static final String SYSTEM = "system";

	}

	/**
	 * 优先级
	 */
	@UtilityClass
	public static class Priority {

		public static final int HIGHEST = 1;

		public static final int HIGH = 10;

		public static final int NORMAL = 50;

		public static final int LOW = 100;

		public static final int LOWEST = Integer.MAX_VALUE;

	}

	/**
	 * 流程类型
	 */
	@UtilityClass
	public static class ProcessType {

		public static final String PLANNED_CUTOVER = "PLANNED_CUTOVER";

		public static final String EMERGENCY_CUTOVER = "EMERGENCY_CUTOVER";

	}

	/**
	 * 流程节点
	 */
	@UtilityClass
	public static class ProcessNode {

		public static final String PLAN_REVIEW = "PLAN_REVIEW";

		public static final String RESOURCE_CHECK = "RESOURCE_CHECK";

		public static final String PROVINCE_APPROVAL = "PROVINCE_APPROVAL";

		public static final String EXECUTE_CUTOVER = "EXECUTE_CUTOVER";

		public static final String RESULT_ACCEPTANCE = "RESULT_ACCEPTANCE";

		public static final String ARCHIVE = "ARCHIVE";

	}

	/**
	 * 流程定义状态 专门用于流程定义（ProcessDefinition）的状态管理
	 */
	@UtilityClass
	public static class ProcessDefinitionStatus {

		/** 草稿状态 - 流程定义刚创建或正在设计中 */
		public static final String DRAFT = "DRAFT";

		/** 运行中 - 流程定义已激活，可以启动流程实例 */
		public static final String ACTIVE = "ACTIVE";

		/** 已暂停 - 流程定义被暂停，不能启动新的流程实例 */
		public static final String SUSPENDED = "SUSPENDED";

		/** 已停用 - 流程定义被停用，不再使用 */
		public static final String DISABLED = "DISABLED";

	}

	/**
	 * 流程实例状态 专门用于流程实例（ProcessInstance）的状态管理
	 */
	@UtilityClass
	public static class ProcessInstanceStatus {

		/** 运行中 */
		public static final String RUNNING = "RUNNING";

		/** 已完成 */
		public static final String COMPLETED = "COMPLETED";

		/** 已终止 */
		public static final String TERMINATED = "TERMINATED";

		/** 已暂停 */
		public static final String SUSPENDED = "SUSPENDED";

		/** 异常终止 */
		public static final String FAILED = "FAILED";

	}

	/**
	 * 流程分类
	 */
	@UtilityClass
	public static class ProcessCategory {

		public static final String WORKFLOW = "WORKFLOW";

		public static final String APPROVAL = "APPROVAL";

		public static final String BUSINESS = "BUSINESS";

	}

	/**
	 * 设计器类型
	 */
	@UtilityClass
	public static class DesignerType {

		public static final String BPMN_JS = "BPMN_JS";

		public static final String CAMUNDA_MODELER = "CAMUNDA_MODELER";

		public static final String CUSTOM = "CUSTOM";

	}

	/**
	 * BPMN XML 相关常量
	 */
	@UtilityClass
	public static class BpmnXml {

		public static final String XML_DECLARATION_PREFIX = "<?xml";

		public static final String BPMN_PREFIX = "<bpmn";

		public static final String BPMN_DEFINITIONS = "bpmn:definitions";

		public static final String DEFINITIONS = "definitions";

		public static final String BPMN_PROCESS = "bpmn:process";

		public static final String PROCESS = "process";

		public static final String FILE_EXTENSION = ".bpmn";

		public static final String VERSION_SEPARATOR = "_v";

	}

	/**
	 * 正则表达式模式
	 */
	@UtilityClass
	public static class RegexPattern {

		/**
		 * BPMN流程Key提取正则表达式
		 */
		public static final String PROCESS_ID_PATTERN = "<bpmn:process[^>]+id=\"([^\"]+)\"";

		/**
		 * BPMN流程名称提取正则表达式
		 */
		public static final String PROCESS_NAME_PATTERN = "<bpmn:process[^>]+name=\"([^\"]+)\"";

	}

}
