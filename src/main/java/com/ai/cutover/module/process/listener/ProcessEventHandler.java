package com.ai.cutover.module.process.listener;

import java.util.Map;

/**
 * 流程事件处理器接口 业务方可以实现此接口来处理流程事件
 *
 * <AUTHOR>
 */
public interface ProcessEventHandler {

	/**
	 * 流程启动后事件
	 * @param processInstanceId 流程实例ID
	 * @param processKey 流程定义Key
	 * @param businessKey 业务Key
	 * @param initiatorId 发起人ID
	 * @param variables 流程变量
	 */
	default void onProcessStarted(String processInstanceId, String processKey, String businessKey, String initiatorId,
			Map<String, Object> variables) {
		// 默认空实现
	}

	/**
	 * 任务创建事件
	 * @param taskId 任务ID
	 * @param taskKey 任务Key
	 * @param taskName 任务名称
	 * @param assigneeId 分配人ID
	 * @param processInstanceId 流程实例ID
	 */
	default void onTaskCreated(String taskId, String taskKey, String taskName, String assigneeId,
			String processInstanceId) {
		// 默认空实现
	}

	/**
	 * 任务完成事件
	 * @param taskId 任务ID
	 * @param taskKey 任务Key
	 * @param taskName 任务名称
	 * @param userId 完成人ID
	 * @param processInstanceId 流程实例ID
	 * @param variables 任务变量
	 */
	default void onTaskCompleted(String taskId, String taskKey, String taskName, String userId,
			String processInstanceId, Map<String, Object> variables) {
		// 默认空实现
	}

	/**
	 * 审批事件
	 * @param taskId 任务ID
	 * @param taskKey 任务Key
	 * @param approved 是否通过
	 * @param comment 审批意见
	 * @param userId 审批人ID
	 * @param processInstanceId 流程实例ID
	 */
	default void onTaskApproved(String taskId, String taskKey, boolean approved, String comment, String userId,
			String processInstanceId) {
		// 默认空实现
	}

	/**
	 * 流程完成事件
	 * @param processInstanceId 流程实例ID
	 * @param processKey 流程定义Key
	 * @param businessKey 业务Key
	 */
	default void onProcessCompleted(String processInstanceId, String processKey, String businessKey) {
		// 默认空实现
	}

	/**
	 * 流程终止事件
	 * @param processInstanceId 流程实例ID
	 * @param processKey 流程定义Key
	 * @param reason 终止原因
	 */
	default void onProcessTerminated(String processInstanceId, String processKey, String reason) {
		// 默认空实现
	}

	/**
	 * 任务委派事件
	 * @param taskId 任务ID
	 * @param fromUserId 委派人ID
	 * @param toUserId 被委派人ID
	 */
	default void onTaskDelegated(String taskId, String fromUserId, String toUserId) {
		// 默认空实现
	}

	/**
	 * 任务转办事件
	 * @param taskId 任务ID
	 * @param fromUserId 转办人ID
	 * @param toUserId 接收人ID
	 */
	default void onTaskAssigned(String taskId, String fromUserId, String toUserId) {
		// 默认空实现
	}

	/**
	 * 获取处理器支持的流程Key 如果返回null或空，表示支持所有流程 注意：由于流程是动态设计的，流程Key可能无法获取，此时会传入null
	 * @return 支持的流程Key列表
	 */
	default String[] getSupportedProcessKeys() {
		return null;
	}

	/**
	 * 是否支持该流程和任务 当无法通过流程Key判断时，可以通过任务Key或其他信息判断
	 * @param processKey 流程定义Key（可能为null）
	 * @param taskKey 任务Key（可能为null）
	 * @param processInstanceId 流程实例ID
	 * @param variables 流程变量
	 * @return 是否支持
	 */
	default boolean supports(String processKey, String taskKey, String processInstanceId,
			Map<String, Object> variables) {
		// 默认通过流程Key判断
		String[] supportedKeys = getSupportedProcessKeys();
		if (supportedKeys == null || supportedKeys.length == 0) {
			return true; // 支持所有流程
		}

		if (processKey != null) {
			for (String key : supportedKeys) {
				if (key.equals(processKey)) {
					return true;
				}
			}
		}

		return false;
	}

	/**
	 * 获取处理器优先级 数值越小优先级越高
	 * @return 优先级
	 */
	default int getOrder() {
		return 0;
	}

	/**
	 * 获取处理器名称
	 * @return 处理器名称
	 */
	default String getName() {
		return this.getClass().getSimpleName();
	}

}
