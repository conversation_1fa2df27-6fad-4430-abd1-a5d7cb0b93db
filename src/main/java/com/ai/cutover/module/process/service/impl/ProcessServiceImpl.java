package com.ai.cutover.module.process.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.ai.cutover.common.constant.CommonConstants;
import com.ai.cutover.common.constant.ErrorMessages;
import com.ai.cutover.common.exception.BusinessException;
import com.ai.cutover.module.process.constant.ProcessConstants;
import com.ai.cutover.module.process.listener.ProcessEventHandler;
import com.ai.cutover.module.process.model.resp.ActivityInfoResp;
import com.ai.cutover.module.process.model.resp.ProcessHistoryInfoResp;
import com.ai.cutover.module.process.model.resp.TaskInfoResp;
import com.ai.cutover.module.process.service.ProcessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.*;
import org.camunda.bpm.engine.history.HistoricActivityInstance;
import org.camunda.bpm.engine.history.HistoricProcessInstance;
import org.camunda.bpm.engine.repository.ProcessDefinition;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程服务实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProcessServiceImpl implements ProcessService {

	private final RuntimeService runtimeService;

	private final TaskService taskService;

	private final HistoryService historyService;

	private final RepositoryService repositoryService;

	@Autowired(required = false)
	private List<ProcessEventHandler> eventHandlers;

	@Override
	public String startProcess(String processDefinitionKey, Long businessKey) {
		return startProcess(processDefinitionKey, businessKey, new HashMap<>());
	}

	@Override
	public String startProcess(String processDefinitionKey, Long businessKey, Map<String, Object> variables) {
		// 设置流程变量
		if (ObjectUtil.isNull(variables)) {
			variables = new HashMap<>();
		}
		variables.put(ProcessConstants.VariableKey.BUSINESS_KEY, businessKey);

		ProcessInstance processInstance;
		if (businessKey != null) {
			processInstance = runtimeService.startProcessInstanceByKey(processDefinitionKey, businessKey.toString(),
					variables);
		}
		else {
			processInstance = runtimeService.startProcessInstanceByKey(processDefinitionKey, variables);
		}

		String processInstanceId = processInstance.getId();

		// 触发流程启动事件
		triggerProcessStartedEvent(processInstanceId, processDefinitionKey,
				businessKey != null ? businessKey.toString() : null, getCurrentUserId(variables), variables);

		return processInstanceId;
	}

	@Override
	public void completeTask(String taskId, Map<String, Object> variables) {
		if (variables != null && !variables.isEmpty()) {
			taskService.complete(taskId, variables);
		}
		else {
			taskService.complete(taskId);
		}
	}

	@Override
	public void approveTask(String taskId, boolean approved, String comment, String userId) {
		// 设置审批变量
		Map<String, Object> variables = new HashMap<>();
		variables.put(ProcessConstants.VariableKey.APPROVED, approved);
		variables.put(ProcessConstants.VariableKey.APPROVAL_RESULT,
				approved ? CommonConstants.ApprovalResult.APPROVED : CommonConstants.ApprovalResult.REJECTED);
		variables.put(ProcessConstants.VariableKey.APPROVAL_COMMENT, comment);
		variables.put(ProcessConstants.VariableKey.APPROVER, userId);
		variables.put(ProcessConstants.VariableKey.APPROVAL_TIME,
				DateUtil.format(new Date(), CommonConstants.DateTimeFormat.YYYY_MM_DD_HH_MM_SS));

		// 添加审批意见
		if (comment != null && !comment.trim().isEmpty()) {
			taskService.createComment(taskId, null, comment);
		}

		// 完成任务
		taskService.complete(taskId, variables);
	}

	@Override
	public List<Task> getUserTasks(String userId) {
		return taskService.createTaskQuery().taskAssignee(userId).orderByTaskCreateTime().desc().list();
	}

	@Override
	public List<Task> getProcessTasks(String processInstanceId) {
		log.info("查询流程任务: processInstanceId={}", processInstanceId);

		return taskService.createTaskQuery().processInstanceId(processInstanceId).orderByTaskCreateTime().desc().list();
	}

	@Override
	public ProcessInstance getProcessInstance(String processInstanceId) {
		log.info("查询流程实例: processInstanceId={}", processInstanceId);

		try {
			return runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
		}
		catch (Exception e) {
			log.error("查询流程实例失败", e);
			throw new BusinessException(ErrorMessages.Process.PROCESS_INSTANCE_QUERY_FAILED, e.getMessage());
		}
	}

	@Override
	public ProcessHistoryInfoResp getProcessHistory(String processInstanceId) {
		log.info("查询流程历史: processInstanceId={}", processInstanceId);

		try {
			// 查询历史流程实例
			HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
				.processInstanceId(processInstanceId)
				.singleResult();

			if (historicProcessInstance == null) {
				throw new BusinessException(ErrorMessages.Process.PROCESS_INSTANCE_NOT_FOUND, processInstanceId);
			}

			// 查询历史活动
			List<HistoricActivityInstance> historicActivities = historyService.createHistoricActivityInstanceQuery()
				.processInstanceId(processInstanceId)
				.orderByHistoricActivityInstanceStartTime()
				.asc()
				.list();

			// 构建返回结果
			ProcessHistoryInfoResp historyInfo = new ProcessHistoryInfoResp();
			historyInfo.setProcessInstanceId(processInstanceId);
			historyInfo.setProcessDefinitionKey(historicProcessInstance.getProcessDefinitionKey());
			historyInfo.setStartTime(DateUtil.format(historicProcessInstance.getStartTime(),
					CommonConstants.DateTimeFormat.YYYY_MM_DD_HH_MM_SS));

			if (historicProcessInstance.getEndTime() != null) {
				historyInfo.setEndTime(DateUtil.format(historicProcessInstance.getEndTime(),
						CommonConstants.DateTimeFormat.YYYY_MM_DD_HH_MM_SS));
				long duration = historicProcessInstance.getEndTime().getTime()
						- historicProcessInstance.getStartTime().getTime();
				historyInfo.setDuration(formatDuration(duration));
			}

			// 转换活动信息
			List<ActivityInfoResp> activities = historicActivities.stream()
				.map(this::convertToActivityInfo)
				.collect(Collectors.toList());
			historyInfo.setActivities(activities);

			return historyInfo;
		}
		catch (Exception e) {
			log.error("查询流程历史失败", e);
			throw new BusinessException(ErrorMessages.Process.PROCESS_HISTORY_QUERY_FAILED, e.getMessage());
		}
	}

	@Override
	public void terminateProcess(String processInstanceId, String reason) {
		log.info("终止流程: processInstanceId={}, reason={}", processInstanceId, reason);

		// 获取流程定义Key用于事件触发
		String processKey = getProcessDefinitionKey(processInstanceId);

		try {
			runtimeService.deleteProcessInstance(processInstanceId, reason);
			log.info("流程终止成功: processInstanceId={}", processInstanceId);

			// 触发流程终止事件
			if (processKey != null) {
				triggerProcessTerminatedEvent(processInstanceId, processKey, reason);
			}
		}
		catch (Exception e) {
			log.error("终止流程失败", e);
			throw new BusinessException(ErrorMessages.Process.PROCESS_TERMINATE_FAILED, e.getMessage());
		}
	}

	@Override
	public void suspendProcess(String processInstanceId) {
		log.info("挂起流程: processInstanceId={}", processInstanceId);

		try {
			runtimeService.suspendProcessInstanceById(processInstanceId);
			log.info("流程挂起成功: processInstanceId={}", processInstanceId);
		}
		catch (Exception e) {
			log.error("挂起流程失败", e);
			throw new BusinessException(ErrorMessages.Process.PROCESS_SUSPEND_FAILED, e.getMessage());
		}
	}

	@Override
	public void activateProcess(String processInstanceId) {
		log.info("激活流程: processInstanceId={}", processInstanceId);

		try {
			runtimeService.activateProcessInstanceById(processInstanceId);
			log.info("流程激活成功: processInstanceId={}", processInstanceId);
		}
		catch (Exception e) {
			log.error("激活流程失败", e);
			throw new BusinessException(ErrorMessages.Process.PROCESS_ACTIVATE_FAILED, e.getMessage());
		}
	}

	@Override
	public Map<String, Object> getProcessVariables(String processInstanceId) {
		try {
			return runtimeService.getVariables(processInstanceId);
		}
		catch (Exception e) {
			log.error("查询流程变量失败", e);
			throw new BusinessException(ErrorMessages.Process.PROCESS_VARIABLES_QUERY_FAILED, e.getMessage());
		}
	}

	@Override
	public void setProcessVariables(String processInstanceId, Map<String, Object> variables) {
		log.info("设置流程变量: processInstanceId={}", processInstanceId);

		try {
			runtimeService.setVariables(processInstanceId, variables);
			log.info("流程变量设置成功: processInstanceId={}", processInstanceId);
		}
		catch (Exception e) {
			log.error("设置流程变量失败", e);
			throw new BusinessException(ErrorMessages.Process.PROCESS_VARIABLES_SET_FAILED, e.getMessage());
		}
	}

	@Override
	public TaskInfoResp getTaskInfo(String taskId) {
		log.info("查询任务详情: taskId={}", taskId);

		try {
			Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
			if (task == null) {
				throw new BusinessException(ErrorMessages.Process.TASK_NOT_FOUND, taskId);
			}

			TaskInfoResp taskInfo = TaskInfoResp.builder()
				.taskId(task.getId())
				.taskName(task.getName())
				.processInstanceId(task.getProcessInstanceId())
				.assignee(task.getAssignee())
				.createTime(DateUtil.format(task.getCreateTime(), CommonConstants.DateTimeFormat.YYYY_MM_DD_HH_MM_SS))
				.dueDate(task.getDueDate() != null
						? DateUtil.format(task.getDueDate(), CommonConstants.DateTimeFormat.YYYY_MM_DD_HH_MM_SS) : null)
				.build();

			// 获取任务变量
			Map<String, Object> variables = taskService.getVariables(taskId);
			taskInfo.setVariables(variables);

			return taskInfo;
		}
		catch (Exception e) {
			log.error("查询任务详情失败", e);
			throw new BusinessException(ErrorMessages.Process.TASK_INFO_QUERY_FAILED, e.getMessage());
		}
	}

	@Override
	public void delegateTask(String taskId, String userId) {
		log.info("委派任务: taskId={}, userId={}", taskId, userId);

		try {
			taskService.delegateTask(taskId, userId);
			log.info("任务委派成功: taskId={}", taskId);
		}
		catch (Exception e) {
			log.error("委派任务失败", e);
			throw new BusinessException(ErrorMessages.Process.TASK_DELEGATE_FAILED, e.getMessage());
		}
	}

	@Override
	public void assignTask(String taskId, String userId) {
		log.info("转办任务: taskId={}, userId={}", taskId, userId);

		try {
			taskService.setAssignee(taskId, userId);
			log.info("任务转办成功: taskId={}", taskId);
		}
		catch (Exception e) {
			log.error("转办任务失败", e);
			throw new BusinessException(ErrorMessages.Process.TASK_ASSIGN_FAILED, e.getMessage());
		}
	}

	/**
	 * 转换活动信息
	 */
	private ActivityInfoResp convertToActivityInfo(HistoricActivityInstance activity) {
		String endTime = null;
		String duration = null;

		if (activity.getEndTime() != null) {
			endTime = DateUtil.format(activity.getEndTime(), CommonConstants.DateTimeFormat.YYYY_MM_DD_HH_MM_SS);
			long durationMillis = activity.getEndTime().getTime() - activity.getStartTime().getTime();
			duration = formatDuration(durationMillis);
		}

		return ActivityInfoResp.builder()
			.activityId(activity.getActivityId())
			.activityName(activity.getActivityName())
			.activityType(activity.getActivityType())
			.startTime(DateUtil.format(activity.getStartTime(), CommonConstants.DateTimeFormat.YYYY_MM_DD_HH_MM_SS))
			.endTime(endTime)
			.duration(duration)
			.assignee(activity.getAssignee())
			.build();
	}

	/**
	 * 格式化持续时间
	 */
	private String formatDuration(long durationMillis) {
		long seconds = durationMillis / 1000;
		long minutes = seconds / 60;
		long hours = minutes / 60;
		long days = hours / 24;

		if (days > 0) {
			return StrUtil.format(CommonConstants.FormatConstants.TIME_FORMAT_DAYS_HOURS_MINUTES, days, hours % 24,
					minutes % 60);
		}
		else if (hours > 0) {
			return StrUtil.format(CommonConstants.FormatConstants.TIME_FORMAT_HOURS_MINUTES, hours, minutes % 60);
		}
		else if (minutes > 0) {
			return StrUtil.format(CommonConstants.FormatConstants.TIME_FORMAT_MINUTES_SECONDS, minutes, seconds % 60);
		}
		else {
			return StrUtil.format(CommonConstants.FormatConstants.TIME_FORMAT_SECONDS, seconds);
		}
	}

	/**
	 * 触发流程启动事件
	 */
	private void triggerProcessStartedEvent(String processInstanceId, String processKey, String businessKey,
			String initiatorId, Map<String, Object> variables) {
		if (eventHandlers != null) {
			eventHandlers.stream()
				.filter(handler -> handler.supports(processKey, null, processInstanceId, variables))
				.sorted(java.util.Comparator.comparingInt(ProcessEventHandler::getOrder))
				.forEach(handler -> {
					try {
						handler.onProcessStarted(processInstanceId, processKey, businessKey, initiatorId, variables);
					}
					catch (Exception e) {
						log.warn("流程启动事件处理失败: handler={}, processInstanceId={}", handler.getName(), processInstanceId,
								e);
					}
				});
		}
	}

	/**
	 * 触发流程完成事件
	 */
	private void triggerProcessCompletedEvent(String processInstanceId, String processKey, String businessKey) {
		if (eventHandlers != null) {
			Map<String, Object> variables = getProcessVariables(processInstanceId);
			eventHandlers.stream()
				.filter(handler -> handler.supports(processKey, null, processInstanceId, variables))
				.sorted(java.util.Comparator.comparingInt(ProcessEventHandler::getOrder))
				.forEach(handler -> {
					try {
						handler.onProcessCompleted(processInstanceId, processKey, businessKey);
					}
					catch (Exception e) {
						log.warn("流程完成事件处理失败: handler={}, processInstanceId={}", handler.getName(), processInstanceId,
								e);
					}
				});
		}
	}

	/**
	 * 触发流程终止事件
	 */
	private void triggerProcessTerminatedEvent(String processInstanceId, String processKey, String reason) {
		if (eventHandlers != null) {
			Map<String, Object> variables = null;
			try {
				variables = getProcessVariables(processInstanceId);
			}
			catch (Exception e) {
				log.warn("获取流程变量失败: processInstanceId={}", processInstanceId, e);
			}

			final Map<String, Object> finalVariables = variables;
			eventHandlers.stream()
				.filter(handler -> handler.supports(processKey, null, processInstanceId, finalVariables))
				.sorted(java.util.Comparator.comparingInt(ProcessEventHandler::getOrder))
				.forEach(handler -> {
					try {
						handler.onProcessTerminated(processInstanceId, processKey, reason);
					}
					catch (Exception e) {
						log.warn("流程终止事件处理失败: handler={}, processInstanceId={}", handler.getName(), processInstanceId,
								e);
					}
				});
		}
	}

	/**
	 * 获取当前用户ID
	 */
	private String getCurrentUserId(Map<String, Object> variables) {
		if (variables != null) {
			Object userId = variables.get(ProcessConstants.VariableKey.CURRENT_USER_ID);
			if (userId != null) {
				return userId.toString();
			}

			Object initiator = variables.get(ProcessConstants.VariableKey.INITIATOR);
			if (initiator != null) {
				return initiator.toString();
			}
		}
		return ProcessConstants.DefaultApprover.SYSTEM;
	}

	/**
	 * 获取流程定义Key
	 */
	private String getProcessDefinitionKey(String processInstanceId) {
		try {
			ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
				.processInstanceId(processInstanceId)
				.singleResult();

			if (processInstance != null) {
				String processDefinitionId = processInstance.getProcessDefinitionId();
				ProcessDefinition processDefinition = repositoryService.getProcessDefinition(processDefinitionId);
				return processDefinition.getKey();
			}

			// 查询历史流程实例
			HistoricProcessInstance historicProcessInstance = historyService.createHistoricProcessInstanceQuery()
				.processInstanceId(processInstanceId)
				.singleResult();

			if (historicProcessInstance != null) {
				return historicProcessInstance.getProcessDefinitionKey();
			}
		}
		catch (Exception e) {
			log.warn("获取流程定义Key失败: processInstanceId={}", processInstanceId, e);
		}
		return null;
	}

}
