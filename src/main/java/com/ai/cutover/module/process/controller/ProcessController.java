package com.ai.cutover.module.process.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.ai.cutover.common.enums.ResourceType;
import com.ai.cutover.common.util.Assert;
import com.ai.cutover.module.process.model.req.*;
import com.ai.cutover.module.process.model.resp.ProcessHistoryInfoResp;
import com.ai.cutover.module.process.model.resp.TaskInfoResp;
import com.ai.cutover.module.process.model.dto.ProcessInstanceDTO;
import com.ai.cutover.module.process.model.dto.TaskDTO;
import com.ai.cutover.module.process.service.ProcessService;
import com.ai.cutover.module.process.util.TaskConverter;
import io.github.linpeilie.Converter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.camunda.bpm.engine.runtime.ProcessInstance;
import org.camunda.bpm.engine.task.Task;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;

import java.util.List;
import java.util.Map;

/**
 * 流程控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/process")
@RequiredArgsConstructor
@Validated
public class ProcessController {

	private final ProcessService processService;

	private final TaskConverter taskConverter;

	private final Converter converter;

	/**
	 * 启动流程
	 */
	@PostMapping("/start")
	public String startProcess(@Valid @RequestBody StartProcessReq request) {
		return processService.startProcess(request.getProcessDefinitionKey(), request.getBusinessKey(),
				request.getVariables());
	}

	/**
	 * 完成任务
	 */
	@PostMapping("/complete-task")
	public void completeTask(@RequestBody CompleteTaskReq request) {
		processService.completeTask(request.getTaskId(), request.getVariables());
	}

	/**
	 * 审批任务
	 */
	@PostMapping("/approve-task")
	public void approveTask(@RequestBody ApproveTaskReq request) {
		String currentUserId = StpUtil.getLoginIdAsString();
		processService.approveTask(request.getTaskId(), request.getApproved(), request.getComment(), currentUserId);
	}

	/**
	 * 查询当前用户待办任务
	 */
	@GetMapping("/user-tasks")
	public List<TaskDTO> getUserTasks() {
		String currentUserId = StpUtil.getLoginIdAsString();
		List<Task> tasks = processService.getUserTasks(currentUserId);
		return taskConverter.toTaskDTOList(tasks);
	}

	/**
	 * 查询流程任务
	 */
	@GetMapping("/process-tasks")
	public List<TaskDTO> getProcessTasks(@RequestParam @NotBlank String processInstanceId) {
		List<Task> tasks = processService.getProcessTasks(processInstanceId);
		return taskConverter.toTaskDTOList(tasks);
	}

	/**
	 * 查询流程实例
	 */
	@GetMapping("/instance")
	public ProcessInstanceDTO getProcessInstance(@RequestParam @NotBlank String processInstanceId) {
		ProcessInstance processInstance = processService.getProcessInstance(processInstanceId);
		Assert.resourceExists(processInstance, ResourceType.PROCESS_INSTANCE);
		return converter.convert(processInstance, ProcessInstanceDTO.class);
	}

	/**
	 * 查询流程历史
	 */
	@GetMapping("/history")
	public ProcessHistoryInfoResp getProcessHistory(@RequestParam @NotBlank String processInstanceId) {
		return processService.getProcessHistory(processInstanceId);
	}

	/**
	 * 终止流程
	 */
	@PostMapping("/terminate")
	public void terminateProcess(@RequestBody TerminateProcessReq request) {
		processService.terminateProcess(request.getProcessInstanceId(), request.getReason());
	}

	/**
	 * 挂起流程
	 */
	@PostMapping("/suspend")
	public void suspendProcess(@RequestBody SuspendProcessReq request) {
		processService.suspendProcess(request.getProcessInstanceId());
	}

	/**
	 * 激活流程
	 */
	@PostMapping("/activate")
	public void activateProcess(@RequestBody ActivateProcessReq request) {
		processService.activateProcess(request.getProcessInstanceId());
	}

	/**
	 * 查询流程变量
	 */
	@GetMapping("/variables")
	public Map<String, Object> getProcessVariables(@RequestParam @NotBlank String processInstanceId) {
		return processService.getProcessVariables(processInstanceId);
	}

	/**
	 * 设置流程变量
	 */
	@PostMapping("/set-variables")
	public void setProcessVariables(@RequestBody SetVariablesReq request) {
		processService.setProcessVariables(request.getProcessInstanceId(), request.getVariables());
	}

	/**
	 * 查询任务详情
	 */
	@GetMapping("/task-info")
	public TaskInfoResp getTaskInfo(@RequestParam @NotBlank String taskId) {
		return processService.getTaskInfo(taskId);
	}

	/**
	 * 委派任务
	 */
	@PostMapping("/delegate-task")
	public void delegateTask(@RequestBody DelegateTaskReq request) {
		processService.delegateTask(request.getTaskId(), request.getDelegateId());
	}

	/**
	 * 转办任务
	 */
	@PostMapping("/assign-task")
	public void assignTask(@RequestBody AssignTaskReq request) {
		processService.assignTask(request.getTaskId(), request.getAssigneeId());
	}

}
