package com.ai.cutover.module.process.model.req;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import java.util.Map;

/**
 * 启动流程请求
 *
 * <AUTHOR>
 */
@Data
public class StartProcessReq {

	/**
	 * 流程定义Key
	 */
	@NotBlank(message = "流程定义Key不能为空")
	private String processDefinitionKey;

	/**
	 * 业务Key
	 */
	private Long businessKey;

	/**
	 * 流程变量
	 */
	private Map<String, Object> variables;

}
