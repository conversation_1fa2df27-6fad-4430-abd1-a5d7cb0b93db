package com.ai.cutover.module.process.model.req;

import lombok.Builder;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

/**
 * 审批任务请求
 *
 * <AUTHOR>
 */
@Data
@Builder
public class ApproveTaskReq {

	/**
	 * 任务ID
	 */
	@NotBlank(message = "任务ID不能为空")
	private String taskId;

	/**
	 * 是否审批通过
	 */
	@NotNull(message = "审批结果不能为空")
	private Boolean approved;

	/**
	 * 审批意见
	 */
	private String comment;

}
