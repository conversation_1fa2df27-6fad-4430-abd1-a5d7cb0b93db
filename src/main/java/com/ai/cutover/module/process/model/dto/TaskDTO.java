package com.ai.cutover.module.process.model.dto;

import lombok.Data;
import org.camunda.bpm.engine.task.Task;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

/**
 * 任务DTO
 *
 * <AUTHOR>
 */
@Data
public class TaskDTO {

	/**
	 * 任务ID
	 */
	private String id;

	/**
	 * 任务名称
	 */
	private String name;

	/**
	 * 任务描述
	 */
	private String description;

	/**
	 * 任务分配人
	 */
	private String assignee;

	/**
	 * 任务候选人
	 */
	private String owner;

	/**
	 * 流程实例ID
	 */
	private String processInstanceId;

	/**
	 * 流程定义ID
	 */
	private String processDefinitionId;

	/**
	 * 任务定义Key
	 */
	private String taskDefinitionKey;

	/**
	 * 创建时间
	 */
	private LocalDateTime createTime;

	/**
	 * 到期时间
	 */
	private LocalDateTime dueDate;

	/**
	 * 优先级
	 */
	private Integer priority;

	/**
	 * 任务变量
	 */
	private Map<String, Object> variables;

	/**
	 * 从Camunda Task对象创建TaskDTO
	 * @param task Camunda Task对象
	 * @return TaskDTO对象
	 */
	public static TaskDTO fromTask(Task task) {
		if (task == null) {
			return null;
		}

		TaskDTO taskDTO = new TaskDTO();
		taskDTO.setId(task.getId());
		taskDTO.setName(task.getName());
		taskDTO.setDescription(task.getDescription());
		taskDTO.setAssignee(task.getAssignee());
		taskDTO.setOwner(task.getOwner());
		taskDTO.setProcessInstanceId(task.getProcessInstanceId());
		taskDTO.setProcessDefinitionId(task.getProcessDefinitionId());
		taskDTO.setTaskDefinitionKey(task.getTaskDefinitionKey());
		taskDTO.setPriority(task.getPriority());

		// 转换时间字段
		if (task.getCreateTime() != null) {
			taskDTO.setCreateTime(convertToLocalDateTime(task.getCreateTime()));
		}
		if (task.getDueDate() != null) {
			taskDTO.setDueDate(convertToLocalDateTime(task.getDueDate()));
		}

		return taskDTO;
	}

	/**
	 * 将Date转换为LocalDateTime
	 */
	private static LocalDateTime convertToLocalDateTime(Date date) {
		if (date == null) {
			return null;
		}
		return date.toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
	}

}
