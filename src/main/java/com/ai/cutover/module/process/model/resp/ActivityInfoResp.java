package com.ai.cutover.module.process.model.resp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 活动信息响应
 *
 * <AUTHOR>
 */
@Data
@Builder
public class ActivityInfoResp {

	/**
	 * 活动ID
	 */
	private String activityId;

	/**
	 * 活动名称
	 */
	private String activityName;

	/**
	 * 活动类型
	 */
	private String activityType;

	/**
	 * 开始时间
	 */
	private String startTime;

	/**
	 * 结束时间
	 */
	private String endTime;

	/**
	 * 持续时间
	 */
	private String duration;

	/**
	 * 处理人
	 */
	private String assignee;

}
