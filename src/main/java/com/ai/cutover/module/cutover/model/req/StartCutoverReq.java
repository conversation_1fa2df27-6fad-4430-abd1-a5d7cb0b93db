package com.ai.cutover.module.cutover.model.req;

import lombok.Builder;
import lombok.Data;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.List;

/**
 * 启动割接流程请求
 *
 * <AUTHOR>
 */
@Data
@Builder
public class StartCutoverReq {

	/**
	 * 流程Key
	 */
	@NotBlank
	private String processKey;

	/**
	 * 业务Key
	 */
	@NotNull
	private Long businessKey;

	/**
	 * 割接方案
	 */
	@NotBlank
	private String cutoverPlan;

	/**
	 * 计划开始时间
	 */
	@NotBlank
	private String plannedStartTime;

	/**
	 * 计划结束时间
	 */
	@NotBlank
	private String plannedEndTime;

	/**
	 * 风险级别
	 */
	@Builder.Default
	private String riskLevel = "MEDIUM";

	/**
	 * 影响系统
	 */
	@NotNull
	private List<String> affectedSystems;

	/**
	 * 地市代码
	 */
	private String cityCode;

	/**
	 * 联系人
	 */
	private String contactPerson;

	/**
	 * 联系电话
	 */
	private String contactPhone;

	/**
	 * 割接类型
	 */
	@Builder.Default
	private String cutoverType = "PLANNED";

	/**
	 * 紧急原因（紧急割接时必填）
	 */
	private String emergencyReason;

	/**
	 * 紧急级别（紧急割接时必填）
	 */
	private String emergencyLevel;

}
