package com.ai.cutover.module.cutover.constant;

/**
 * 割接业务常量
 *
 * <AUTHOR>
 */
public final class CutoverConstants {

	private CutoverConstants() {
		// 工具类，禁止实例化
	}

	/**
	 * 割接类型
	 */
	public static final class CutoverType {

		public static final String PLANNED = "PLANNED";

		public static final String EMERGENCY = "EMERGENCY";

		public static final String GENERAL = "GENERAL";

		public static final String UNKNOWN = "UNKNOWN";

	}

	/**
	 * 风险级别
	 */
	public static final class RiskLevel {

		public static final String LOW = "LOW";

		public static final String MEDIUM = "MEDIUM";

		public static final String HIGH = "HIGH";

		public static final String CRITICAL = "CRITICAL";

	}

	/**
	 * 紧急级别
	 */
	public static final class EmergencyLevel {

		public static final String LOW = "LOW";

		public static final String MEDIUM = "MEDIUM";

		public static final String HIGH = "HIGH";

		public static final String CRITICAL = "CRITICAL";

	}

	/**
	 * 割接流程变量Key
	 */
	public static final class VariableKey {

		public static final String CUTOVER_TYPE = "cutoverType";

		public static final String CUTOVER_PLAN = "cutoverPlan";

		public static final String PLANNED_START_TIME = "plannedStartTime";

		public static final String PLANNED_END_TIME = "plannedEndTime";

		public static final String RISK_LEVEL = "riskLevel";

		public static final String AFFECTED_SYSTEMS = "affectedSystems";

		public static final String CITY_CODE = "cityCode";

		public static final String CONTACT_PERSON = "contactPerson";

		public static final String CONTACT_PHONE = "contactPhone";

		public static final String EMERGENCY_REASON = "emergencyReason";

		public static final String EMERGENCY_LEVEL = "emergencyLevel";

		public static final String START_TIME = "startTime";

	}

	/**
	 * 任务Key关键字
	 */
	public static final class TaskKeyword {

		public static final String REVIEW = "review";

		public static final String APPROVAL = "approval";

		public static final String EXECUTE = "execute";

		public static final String ACCEPTANCE = "acceptance";

		public static final String CUTOVER = "cutover";

		public static final String MANAGER = "manager";

		public static final String DIRECTOR = "director";

		public static final String SENIOR = "senior";

		public static final String QA = "qa";

		public static final String PLAN = "plan";

	}

	/**
	 * 默认值
	 */
	public static final class DefaultValue {

		public static final String DEFAULT_RISK_LEVEL = RiskLevel.MEDIUM;

		public static final String DEFAULT_CUTOVER_TYPE = CutoverType.PLANNED;

		public static final String DEFAULT_APPROVER = "admin";

	}

	/**
	 * 审批人角色
	 */
	public static final class ApproverRole {

		public static final String MANAGER = "MANAGER";

		public static final String DIRECTOR = "DIRECTOR";

		public static final String EXECUTOR = "EXECUTOR";

		public static final String QA = "QA";

		public static final String EMERGENCY_REVIEWER = "EMERGENCY_REVIEWER";

		public static final String EMERGENCY_APPROVER = "EMERGENCY_APPROVER";

	}

	/**
	 * 审批人前缀
	 */
	public static final class ApproverPrefix {

		public static final String MANAGER = "manager_";

		public static final String DIRECTOR = "director_";

		public static final String EXECUTOR = "executor_";

		public static final String QA_MANAGER = "qa_manager_";

		public static final String EMERGENCY_REVIEWER = "emergency_reviewer_";

		public static final String SENIOR_MANAGER = "senior_manager_";

		public static final String DEPT_MANAGER = "dept_manager_";

		public static final String EMERGENCY_EXECUTOR = "emergency_executor_";

	}

}
