package com.ai.cutover.module.cutover.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ai.cutover.module.cutover.constant.CutoverConstants;

import java.util.Map;

/**
 * 割接业务工具类 提供通用的业务逻辑判断方法，避免循环依赖
 *
 * <AUTHOR>
 */
public final class CutoverBusinessUtil {

	private CutoverBusinessUtil() {
		// 工具类，禁止实例化
	}

	/**
	 * 判断是否是紧急割接
	 * @param cutoverType 割接类型
	 * @return 是否是紧急割接
	 */
	public static boolean isEmergencyCutover(String cutoverType) {
		return CutoverConstants.CutoverType.EMERGENCY.equals(cutoverType);
	}

	/**
	 * 从流程变量中提取割接类型
	 * @param variables 流程变量
	 * @return 割接类型
	 */
	public static String extractCutoverType(Map<String, Object> variables) {
		if (CollUtil.isEmpty(variables)) {
			return CutoverConstants.CutoverType.UNKNOWN;
		}

		Object cutoverType = variables.get(CutoverConstants.VariableKey.CUTOVER_TYPE);
		if (ObjectUtil.isNotNull(cutoverType)) {
			return cutoverType.toString();
		}

		if (variables.containsKey(CutoverConstants.VariableKey.EMERGENCY_REASON)) {
			return CutoverConstants.CutoverType.EMERGENCY;
		}

		if (variables.containsKey(CutoverConstants.VariableKey.PLANNED_START_TIME)) {
			return CutoverConstants.CutoverType.PLANNED;
		}

		return CutoverConstants.CutoverType.GENERAL;
	}

	/**
	 * 检查紧急割接的必填字段
	 * @param variables 流程变量
	 * @return 是否包含必填字段
	 */
	public static boolean hasRequiredEmergencyFields(Map<String, Object> variables) {
		if (CollUtil.isEmpty(variables)) {
			return false;
		}

		return variables.containsKey(CutoverConstants.VariableKey.EMERGENCY_REASON)
				&& variables.containsKey(CutoverConstants.VariableKey.EMERGENCY_LEVEL);
	}

	/**
	 * 判断是否是高级别紧急情况
	 * @param emergencyLevel 紧急级别
	 * @return 是否是高级别紧急情况
	 */
	public static boolean isHighLevelEmergency(String emergencyLevel) {
		return CutoverConstants.EmergencyLevel.HIGH.equals(emergencyLevel)
				|| CutoverConstants.EmergencyLevel.CRITICAL.equals(emergencyLevel);
	}

	/**
	 * 判断是否是严重紧急情况
	 * @param emergencyLevel 紧急级别
	 * @return 是否是严重紧急情况
	 */
	public static boolean isCriticalEmergency(String emergencyLevel) {
		return CutoverConstants.EmergencyLevel.CRITICAL.equals(emergencyLevel);
	}

}
