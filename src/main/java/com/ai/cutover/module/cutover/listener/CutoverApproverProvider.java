package com.ai.cutover.module.cutover.listener;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ai.cutover.module.cutover.constant.CutoverConstants;
import com.ai.cutover.module.cutover.util.CutoverBusinessUtil;
import com.ai.cutover.module.process.constant.ProcessConstants;
import com.ai.cutover.module.process.listener.ApproverProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 割接审批人提供者 专门为割接管控项目提供审批人设置 根据不同的任务类型和割接类型动态设置审批人
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CutoverApproverProvider implements ApproverProvider {

	@Override
	public List<String> getApprovers(String processKey, String taskKey, String processInstanceId,
			Map<String, Object> variables) {
		log.info("获取割接审批人: processKey={}, taskKey={}", processKey, taskKey);

		List<String> approvers = new ArrayList<>();

		// 1. 从variables中提取需要的参数
		String cutoverType = CutoverBusinessUtil.extractCutoverType(variables);
		boolean isEmergency = CutoverBusinessUtil.isEmergencyCutover(cutoverType);

		log.info("割接类型: {}, 是否紧急: {}", cutoverType, isEmergency);

		// 根据任务类型和割接类型设置审批人
		if (taskKey != null) {
			// 2. 提取其他需要的参数
			String emergencyLevel = extractVariableAsString(variables, CutoverConstants.VariableKey.EMERGENCY_LEVEL);
			String cityCode = extractVariableAsString(variables, CutoverConstants.VariableKey.CITY_CODE);
			List<String> affectedSystems = extractAffectedSystems(variables);

			approvers.addAll(getApproversByTaskType(taskKey, isEmergency, emergencyLevel, cityCode, affectedSystems));
		}
		else {
			approvers.addAll(getDefaultApprovers(isEmergency));
		}

		// 如果没有找到审批人，使用默认
		if (approvers.isEmpty()) {
			approvers.add(CutoverConstants.DefaultValue.DEFAULT_APPROVER);
			log.warn("未找到合适的审批人: taskKey={}, cutoverType={}", taskKey, cutoverType);
		}

		log.info("获取到审批人: taskKey={}, cutoverType={}, approvers={}", taskKey, cutoverType, approvers);
		return approvers;
	}

	@Override
	public boolean supports(String processKey, String taskKey) {
		// 项目专门做割接管控，支持所有流程
		return true;
	}

	@Override
	public int getOrder() {
		return ProcessConstants.Priority.HIGH;
	}

	@Override
	public String getName() {
		return "CutoverApproverProvider";
	}

	// 审批人获取方法

	/**
	 * 根据任务类型获取审批人
	 */
	private List<String> getApproversByTaskType(String taskKey, boolean isEmergency, String emergencyLevel,
			String cityCode, List<String> affectedSystems) {
		if (taskKey.contains(CutoverConstants.TaskKeyword.REVIEW)
				|| taskKey.contains(CutoverConstants.TaskKeyword.PLAN)) {
			return isEmergency ? getEmergencyReviewers(emergencyLevel) : getCityManagers(cityCode);
		}

		if (taskKey.contains(CutoverConstants.TaskKeyword.APPROVAL)) {
			return isEmergency ? getEmergencyApprovers(emergencyLevel) : getProvinceManagers();
		}

		if (taskKey.contains(CutoverConstants.TaskKeyword.EXECUTE)) {
			return isEmergency ? getEmergencyExecutors() : getExecutors(affectedSystems);
		}

		if (taskKey.contains(CutoverConstants.TaskKeyword.ACCEPTANCE)
				|| taskKey.contains(CutoverConstants.TaskKeyword.QA)) {
			return getQAManagers();
		}

		// 其他任务类型，根据割接类型设置
		return isEmergency ? getEmergencyApprovers(emergencyLevel) : getCityManagers(cityCode);
	}

	/**
	 * 从流程变量中提取字符串值
	 * @param variables 流程变量
	 * @param key 变量键
	 * @return 字符串值
	 */
	private String extractVariableAsString(Map<String, Object> variables, String key) {
		if (CollUtil.isEmpty(variables)) {
			return null;
		}
		Object value = variables.get(key);
		return ObjectUtil.isNotNull(value) ? value.toString() : null;
	}

	/**
	 * 从流程变量中提取影响系统列表
	 */
	@SuppressWarnings("unchecked")
	private List<String> extractAffectedSystems(Map<String, Object> variables) {
		Object affectedSystemsObj = variables.get(CutoverConstants.VariableKey.AFFECTED_SYSTEMS);
		if (affectedSystemsObj instanceof List) {
			return (List<String>) affectedSystemsObj;
		}
		return null;
	}

	/**
	 * 获取默认审批人
	 */
	private List<String> getDefaultApprovers(boolean isEmergency) {
		List<String> approvers = new ArrayList<>();
		if (isEmergency) {
			// 7. 紧急情况下使用默认紧急审批人，不依赖具体的紧急级别
			approvers.add(CutoverConstants.ApproverPrefix.EMERGENCY_REVIEWER + "001");
			approvers.add(CutoverConstants.ApproverPrefix.SENIOR_MANAGER + "001");
		}
		else {
			approvers.add(CutoverConstants.DefaultValue.DEFAULT_APPROVER);
		}
		return approvers;
	}

	/**
	 * 获取地市管理员
	 */
	private List<String> getCityManagers(String cityCode) {
		List<String> managers = new ArrayList<>();

		if (cityCode != null) {
			// 根据地市代码获取管理员
			managers.addAll(getManagersByCity(cityCode));
		}
		else {
			// 默认地市管理员
			managers.add(CutoverConstants.ApproverPrefix.MANAGER + "001");
			managers.add(CutoverConstants.ApproverPrefix.MANAGER + "002");
		}

		return managers;
	}

	/**
	 * 获取省公司管理员
	 */
	private List<String> getProvinceManagers() {
		List<String> managers = new ArrayList<>();

		// 省公司管理员
		managers.add(CutoverConstants.ApproverPrefix.DIRECTOR + "001");
		managers.add(CutoverConstants.ApproverPrefix.DIRECTOR + "002");

		return managers;
	}

	/**
	 * 获取执行人员
	 */
	private List<String> getExecutors(List<String> affectedSystems) {
		List<String> executors = new ArrayList<>();

		if (affectedSystems != null && !affectedSystems.isEmpty()) {
			for (String system : affectedSystems) {
				executors.add(CutoverConstants.ApproverPrefix.EXECUTOR + system);
			}
		}
		else {
			// 默认执行人员
			executors.add(CutoverConstants.ApproverPrefix.EXECUTOR + "001");
		}

		return executors;
	}

	/**
	 * 获取质量管理员
	 */
	private List<String> getQAManagers() {
		List<String> qaManagers = new ArrayList<>();

		qaManagers.add(CutoverConstants.ApproverPrefix.QA_MANAGER + "001");
		qaManagers.add(CutoverConstants.ApproverPrefix.QA_MANAGER + "002");

		return qaManagers;
	}

	/**
	 * 获取紧急审核人员
	 */
	private List<String> getEmergencyReviewers(String emergencyLevel) {
		List<String> reviewers = new ArrayList<>();

		// 紧急情况下的审核人员，通常是高级管理员
		reviewers.add(CutoverConstants.ApproverPrefix.EMERGENCY_REVIEWER + "001");
		reviewers.add(CutoverConstants.ApproverPrefix.SENIOR_MANAGER + "001");

		if (CutoverBusinessUtil.isCriticalEmergency(emergencyLevel)) {
			// 严重紧急情况需要更高级别的审核
			reviewers.add(CutoverConstants.ApproverPrefix.DIRECTOR + "001");
		}

		return reviewers;
	}

	/**
	 * 获取紧急审批人
	 */
	private List<String> getEmergencyApprovers(String emergencyLevel) {
		List<String> approvers = new ArrayList<>();

		// 紧急审批人员
		approvers.add(CutoverConstants.ApproverPrefix.EMERGENCY_REVIEWER + "001");
		approvers.add(CutoverConstants.ApproverPrefix.SENIOR_MANAGER + "001");

		if (CutoverBusinessUtil.isHighLevelEmergency(emergencyLevel)) {
			// 高级别紧急情况需要更高级别的审批
			approvers.add(CutoverConstants.ApproverPrefix.DIRECTOR + "001");
		}

		return approvers;
	}

	/**
	 * 获取紧急执行人员
	 */
	private List<String> getEmergencyExecutors() {
		List<String> executors = new ArrayList<>();

		// 紧急执行人员，通常是经验丰富的高级执行人员
		executors.add(CutoverConstants.ApproverPrefix.EMERGENCY_EXECUTOR + "001");
		executors.add(CutoverConstants.ApproverPrefix.SENIOR_MANAGER + "001");

		return executors;
	}

	/**
	 * 根据地市代码获取管理员
	 */
	private List<String> getManagersByCity(String cityCode) {
		List<String> managers = new ArrayList<>();

		// 实际实现中应该从数据库或配置中获取
		managers.add(CutoverConstants.ApproverPrefix.DEPT_MANAGER + cityCode + "_001");
		managers.add(CutoverConstants.ApproverPrefix.DEPT_MANAGER + cityCode + "_002");

		return managers;
	}

}
