package com.ai.cutover.module.cutover.service;

import cn.hutool.core.date.DateUtil;
import com.ai.cutover.common.constant.CommonConstants;
import com.ai.cutover.common.constant.ErrorMessages;
import com.ai.cutover.common.constant.StatusConstants;
import com.ai.cutover.common.util.Assert;
import com.ai.cutover.module.cutover.constant.CutoverConstants;
import com.ai.cutover.module.cutover.model.req.StartCutoverReq;
import com.ai.cutover.module.cutover.model.resp.ApproveTaskResp;
import com.ai.cutover.module.cutover.model.resp.CompleteTaskResp;
import com.ai.cutover.module.cutover.model.resp.StartCutoverResp;
import com.ai.cutover.module.cutover.model.resp.TerminateProcessResp;
import com.ai.cutover.module.process.constant.ProcessConstants;
import com.ai.cutover.module.process.service.ProcessService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 割接流程业务服务 封装割接相关的业务逻辑
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CutoverProcessService {

	private final ProcessService processService;

	/**
	 * 启动割接流程
	 * @param request 启动请求
	 * @param initiatorId 发起人ID
	 * @return 启动响应
	 */
	public StartCutoverResp startCutoverProcess(StartCutoverReq request, String initiatorId) {
		// 验证请求参数
		checkStartRequest(request);

		// 构建流程变量
		Map<String, Object> variables = buildProcessVariables(request, initiatorId);

		// 启动流程
		String processInstanceId = processService.startProcess(request.getProcessKey(), request.getBusinessKey(),
				variables);

		// 启动后处理
		afterProcessStart(processInstanceId, request);

		// 构建响应
		return StartCutoverResp.builder()
			.processInstanceId(processInstanceId)
			.businessKey(request.getBusinessKey())
			.cutoverType(request.getCutoverType())
			.processKey(request.getProcessKey())
			.initiatorId(initiatorId)
			.startTime(DateUtil.format(LocalDateTime.now(), CommonConstants.DateTimeFormat.YYYY_MM_DD_HH_MM_SS))
			.build();
	}

	/**
	 * 审批割接任务
	 * @param taskId 任务ID
	 * @param userId 用户ID
	 * @param approved 是否通过
	 * @param comment 审批意见
	 * @return 审批响应
	 */
	public ApproveTaskResp approveCutoverTask(String taskId, String userId, boolean approved, String comment) {
		// 执行审批
		processService.approveTask(taskId, approved, comment, userId);

		// 审批后处理
		afterTaskApproval(taskId, userId, approved, comment);

		// 构建响应
		return ApproveTaskResp.builder()
			.taskId(taskId)
			.approved(approved)
			.approverId(userId)
			.comment(comment)
			.approvalTime(LocalDateTime.now().toString())
			.build();
	}

	/**
	 * 完成割接任务
	 * @param taskId 任务ID
	 * @param userId 用户ID
	 * @param variables 任务变量
	 * @return 完成响应
	 */
	public CompleteTaskResp completeCutoverTask(String taskId, String userId, Map<String, Object> variables) {
		log.info("完成割接任务: taskId={}, userId={}", taskId, userId);

		// 完成任务
		processService.completeTask(taskId, variables);

		// 完成后处理
		afterTaskComplete(taskId, userId, variables);

		// 构建响应
		return CompleteTaskResp.builder()
			.taskId(taskId)
			.executorId(userId)
			.completionTime(LocalDateTime.now().toString())
			.variables(variables)
			.nextTaskInfo("任务已完成，等待下一步流程")
			.build();
	}

	/**
	 * 终止割接流程
	 * @param processInstanceId 流程实例ID
	 * @param reason 终止原因
	 * @param operatorId 操作人ID
	 * @return 终止响应
	 */
	public TerminateProcessResp terminateCutoverProcess(String processInstanceId, String reason, String operatorId) {
		log.info("终止割接流程: processInstanceId={}, reason={}, operator={}", processInstanceId, reason, operatorId);

		// 终止流程
		processService.terminateProcess(processInstanceId, reason);

		// 终止后处理
		afterProcessTerminate(processInstanceId, reason, operatorId);

		// 构建响应
		return TerminateProcessResp.builder()
			.processInstanceId(processInstanceId)
			.reason(reason)
			.operatorId(operatorId)
			.terminationTime(LocalDateTime.now().toString())
			.processStatus(ProcessConstants.ProcessInstanceStatus.TERMINATED)
			.build();
	}

	/**
	 * 检查启动请求参数
	 */
	private void checkStartRequest(StartCutoverReq request) {
		Assert.notNull(request.getBusinessKey(), ErrorMessages.Cutover.BUSINESS_KEY_REQUIRED);
		Assert.hasText(request.getCutoverPlan(), ErrorMessages.Cutover.CUTOVER_PLAN_REQUIRED);
		Assert.notEmpty(request.getAffectedSystems(), ErrorMessages.Cutover.AFFECTED_SYSTEMS_REQUIRED);

		// 检查紧急割接的必填字段
		if (CutoverConstants.CutoverType.EMERGENCY.equals(request.getCutoverType())) {
			checkEmergencyFields(request);
		}
	}

	/**
	 * 检查紧急割接字段
	 */
	private void checkEmergencyFields(StartCutoverReq request) {
		Assert.hasText(request.getEmergencyReason(), ErrorMessages.Cutover.EMERGENCY_REASON_REQUIRED);
		Assert.hasText(request.getEmergencyLevel(), ErrorMessages.Cutover.EMERGENCY_LEVEL_REQUIRED);
	}

	/**
	 * 构建流程变量
	 */
	private Map<String, Object> buildProcessVariables(StartCutoverReq request, String initiatorId) {
		Map<String, Object> variables = new HashMap<>();

		// 基本信息
		variables.put(ProcessConstants.VariableKey.INITIATOR, initiatorId);
		variables.put(CutoverConstants.VariableKey.CUTOVER_PLAN, request.getCutoverPlan());
		variables.put(CutoverConstants.VariableKey.PLANNED_START_TIME, request.getPlannedStartTime());
		variables.put(CutoverConstants.VariableKey.PLANNED_END_TIME, request.getPlannedEndTime());
		variables.put(CutoverConstants.VariableKey.RISK_LEVEL, request.getRiskLevel());
		variables.put(CutoverConstants.VariableKey.AFFECTED_SYSTEMS, request.getAffectedSystems());
		variables.put(CutoverConstants.VariableKey.CITY_CODE, request.getCityCode());
		variables.put(CutoverConstants.VariableKey.CONTACT_PERSON, request.getContactPerson());
		variables.put(CutoverConstants.VariableKey.CONTACT_PHONE, request.getContactPhone());
		variables.put(CutoverConstants.VariableKey.CUTOVER_TYPE, request.getCutoverType());
		variables.put(CutoverConstants.VariableKey.START_TIME,
				DateUtil.format(LocalDateTime.now(), CommonConstants.DateTimeFormat.YYYY_MM_DD_HH_MM_SS));

		// 紧急割接特有信息
		if (CutoverConstants.CutoverType.EMERGENCY.equals(request.getCutoverType())) {
			variables.put(CutoverConstants.VariableKey.EMERGENCY_REASON, request.getEmergencyReason());
			variables.put(CutoverConstants.VariableKey.EMERGENCY_LEVEL, request.getEmergencyLevel());
		}

		return variables;
	}

	/**
	 * 流程启动后处理
	 */
	private void afterProcessStart(String processInstanceId, StartCutoverReq request) {
		log.info("割接流程启动完成: processInstanceId={}, businessKey={}", processInstanceId, request.getBusinessKey());

		// 这里可以添加业务相关的后处理逻辑
		// 比如：发送通知、记录日志、更新业务状态等
	}

	/**
	 * 任务审批后处理
	 */
	private void afterTaskApproval(String taskId, String userId, boolean approved, String comment) {
		log.info("割接任务审批完成: taskId={}, userId={}, approved={}", taskId, userId, approved);

		// 这里可以添加审批后的业务逻辑
		// 比如：发送通知、更新状态等
	}

	/**
	 * 任务完成后处理
	 */
	private void afterTaskComplete(String taskId, String userId, Map<String, Object> variables) {
		log.info("割接任务完成: taskId={}, userId={}", taskId, userId);

		// 这里可以添加任务完成后的业务逻辑
	}

	/**
	 * 流程终止后处理
	 */
	private void afterProcessTerminate(String processInstanceId, String reason, String operatorId) {
		log.warn("割接流程终止: processInstanceId={}, reason={}", processInstanceId, reason);

		// 这里可以添加流程终止后的业务逻辑
	}

}
