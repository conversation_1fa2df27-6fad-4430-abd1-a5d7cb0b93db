package com.ai.cutover.module.cutover.model.resp;

import lombok.Builder;
import lombok.Data;

/**
 * 审批任务响应
 *
 * <AUTHOR>
 */
@Data
@Builder
public class ApproveTaskResp {

	/**
	 * 任务ID
	 */
	private String taskId;

	/**
	 * 审批结果
	 */
	private Boolean approved;

	/**
	 * 审批人ID
	 */
	private String approverId;

	/**
	 * 审批意见
	 */
	private String comment;

	/**
	 * 审批时间
	 */
	private String approvalTime;

}
