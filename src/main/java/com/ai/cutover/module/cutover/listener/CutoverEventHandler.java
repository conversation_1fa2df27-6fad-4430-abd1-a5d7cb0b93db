package com.ai.cutover.module.cutover.listener;

import com.ai.cutover.module.cutover.constant.CutoverConstants;
import com.ai.cutover.module.cutover.util.CutoverBusinessUtil;
import com.ai.cutover.module.process.constant.ProcessConstants;
import com.ai.cutover.module.process.listener.ProcessEventHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 割接流程事件处理器 专门处理割接管控项目的流程事件
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class CutoverEventHandler implements ProcessEventHandler {

	@Override
	public void onProcessStarted(String processInstanceId, String processKey, String businessKey, String initiatorId,
			Map<String, Object> variables) {
		// 从variables中提取需要的参数
		String cutoverType = CutoverBusinessUtil.extractCutoverType(variables);
		boolean isEmergency = CutoverBusinessUtil.isEmergencyCutover(cutoverType);

		log.info("割接流程启动: processInstanceId={}, processKey={}, initiator={}, cutoverType={}, isEmergency={}",
				processInstanceId, processKey, initiatorId, cutoverType, isEmergency);

		// 根据割接类型发送不同的通知
		if (isEmergency) {
			sendEmergencyProcessStartNotification(processInstanceId, processKey, initiatorId, variables);
		}
		else {
			sendProcessStartNotification(processInstanceId, processKey, initiatorId, variables);
		}

		// 记录业务日志
		String message = String.format("%s - 类型: %s", "割接流程已启动", cutoverType);
		recordBusinessLog(processInstanceId, ProcessConstants.EventType.PROCESS_STARTED, message, initiatorId);
	}

	@Override
	public void onTaskCreated(String taskId, String taskKey, String taskName, String assigneeId,
			String processInstanceId) {
		log.info("割接任务创建: taskId={}, taskKey={}, taskName={}, assignee={}, processInstanceId={}", taskId, taskKey,
				taskName, assigneeId, processInstanceId);

		// 记录任务创建日志
		recordTaskLog(taskId, processInstanceId, ProcessConstants.EventType.TASK_CREATED, "割接任务已创建", assigneeId);

		// 发送任务分配通知
		if (assigneeId != null) {
			sendTaskAssignNotification(taskId, taskName, assigneeId);
		}
	}

	@Override
	public void onTaskCompleted(String taskId, String taskKey, String taskName, String userId, String processInstanceId,
			Map<String, Object> variables) {
		log.info("割接任务完成: taskId={}, taskKey={}, taskName={}, user={}, processInstanceId={}", taskId, taskKey, taskName,
				userId, processInstanceId);

		// 记录任务完成日志
		recordTaskLog(taskId, processInstanceId, ProcessConstants.EventType.TASK_COMPLETED, "割接任务已完成", userId);

		// 更新业务状态
		updateBusinessStatus(processInstanceId, taskKey, ProcessConstants.EventType.TASK_COMPLETED, userId);
	}

	@Override
	public void onTaskApproved(String taskId, String taskKey, boolean approved, String comment, String userId,
			String processInstanceId) {
		log.info("割接任务审批: taskId={}, taskKey={}, approved={}, user={}, comment={}", taskId, taskKey, approved, userId,
				comment);

		String action = approved ? ProcessConstants.EventType.TASK_APPROVED : ProcessConstants.EventType.TASK_REJECTED;
		String message = String.format("%s，意见：%s", "割接任务已审批" + (approved ? "通过" : "驳回"), comment);

		// 记录审批日志
		recordTaskLog(taskId, processInstanceId, action, message, userId);

		// 发送审批结果通知
		sendApprovalResultNotification(taskId, taskKey, approved, comment, userId);

		// 如果是驳回，进行特殊处理
		if (!approved) {
			handleApprovalReject(processInstanceId, taskKey, comment, userId);
		}
	}

	@Override
	public void onProcessCompleted(String processInstanceId, String processKey, String businessKey) {
		log.info("割接流程完成: processInstanceId={}, processKey={}, businessKey={}", processInstanceId, processKey,
				businessKey);

		// 记录流程完成日志
		recordProcessLog(processInstanceId, ProcessConstants.EventType.PROCESS_COMPLETED, "割接流程已完成",
				ProcessConstants.DefaultApprover.SYSTEM);

		// 发送流程完成通知
		sendProcessCompletionNotification(processInstanceId, processKey, businessKey);

		// 更新业务状态
		updateBusinessStatus(processInstanceId, null, ProcessConstants.EventType.PROCESS_COMPLETED,
				ProcessConstants.DefaultApprover.SYSTEM);
	}

	@Override
	public void onProcessTerminated(String processInstanceId, String processKey, String reason) {
		log.warn("割接流程终止: processInstanceId={}, processKey={}, reason={}", processInstanceId, processKey, reason);

		// 记录流程终止日志
		recordProcessLog(processInstanceId, ProcessConstants.EventType.PROCESS_TERMINATED, "割接流程已终止：" + reason,
				ProcessConstants.DefaultApprover.SYSTEM);

		// 发送流程终止通知
		sendProcessTerminationNotification(processInstanceId, processKey, reason);

		// 更新业务状态
		updateBusinessStatus(processInstanceId, null, ProcessConstants.EventType.PROCESS_TERMINATED,
				ProcessConstants.DefaultApprover.SYSTEM);
	}

	@Override
	public void onTaskDelegated(String taskId, String fromUserId, String toUserId) {
		log.info("割接任务委派: taskId={}, from={}, to={}", taskId, fromUserId, toUserId);

		// 发送委派通知
		sendTaskDelegateNotification(taskId, fromUserId, toUserId);
	}

	@Override
	public void onTaskAssigned(String taskId, String fromUserId, String toUserId) {
		log.info("割接任务转办: taskId={}, from={}, to={}", taskId, fromUserId, toUserId);

		// 发送转办通知
		sendTaskAssignNotification(taskId, "任务转办", toUserId);
	}

	@Override
	public String[] getSupportedProcessKeys() {
		// 项目专门做割接管控，支持所有流程
		return null;
	}

	@Override
	public boolean supports(String processKey, String taskKey, String processInstanceId,
			Map<String, Object> variables) {
		// 项目专门做割接管控，支持所有流程
		return true;
	}

	@Override
	public int getOrder() {
		return ProcessConstants.Priority.HIGH;
	}

	@Override
	public String getName() {
		return "CutoverEventHandler";
	}

	// 私有辅助方法

	private void sendProcessStartNotification(String processInstanceId, String processKey, String initiatorId,
			Map<String, Object> variables) {
		log.info("发送割接流程启动通知: processInstanceId={}, initiator={}", processInstanceId, initiatorId);
		// 实际实现中应该调用消息服务
	}

	private void sendEmergencyProcessStartNotification(String processInstanceId, String processKey, String initiatorId,
			Map<String, Object> variables) {
		log.warn("发送紧急割接流程启动通知: processInstanceId={}, initiator={}", processInstanceId, initiatorId);
		// 紧急流程需要发送紧急通知（短信、电话等）
		sendUrgentNotification("紧急割接流程已启动", processInstanceId, initiatorId);
	}

	private void sendTaskAssignNotification(String taskId, String taskName, String assigneeId) {
		log.info("发送任务分配通知: taskId={}, taskName={}, assignee={}", taskId, taskName, assigneeId);
		// 实际实现中应该调用消息服务发送通知
	}

	private void sendApprovalResultNotification(String taskId, String taskKey, boolean approved, String comment,
			String userId) {
		log.info("发送审批结果通知: taskId={}, taskKey={}, approved={}, user={}", taskId, taskKey, approved, userId);
		// 实际实现中应该调用消息服务发送通知
	}

	private void sendProcessCompletionNotification(String processInstanceId, String processKey, String businessKey) {
		log.info("发送流程完成通知: processInstanceId={}, processKey={}, businessKey={}", processInstanceId, processKey,
				businessKey);
		// 实际实现中应该调用消息服务发送通知
	}

	private void sendProcessTerminationNotification(String processInstanceId, String processKey, String reason) {
		log.warn("发送流程终止通知: processInstanceId={}, processKey={}, reason={}", processInstanceId, processKey, reason);
		// 实际实现中应该调用消息服务发送通知
	}

	private void sendTaskDelegateNotification(String taskId, String fromUserId, String toUserId) {
		log.info("发送任务委派通知: taskId={}, from={}, to={}", taskId, fromUserId, toUserId);
		// 实际实现中应该调用消息服务发送通知
	}

	private void sendUrgentNotification(String message, String processInstanceId, String userId) {
		log.warn("发送紧急通知: message={}, processInstanceId={}, user={}", message, processInstanceId, userId);
		// 实际实现中应该调用紧急通知服务（短信、电话等）
	}

	private void recordProcessLog(String processInstanceId, String action, String message, String userId) {
		log.info("记录流程日志: processInstanceId={}, action={}, message={}, user={}", processInstanceId, action, message,
				userId);
		// 实际实现中应该记录到数据库或日志系统
	}

	private void recordTaskLog(String taskId, String processInstanceId, String action, String message, String userId) {
		log.info("记录任务日志: taskId={}, processInstanceId={}, action={}, message={}, user={}", taskId, processInstanceId,
				action, message, userId);
		// 实际实现中应该记录到数据库或日志系统
	}

	private void recordBusinessLog(String processInstanceId, String action, String message, String userId) {
		log.info("记录业务日志: processInstanceId={}, action={}, message={}, user={}", processInstanceId, action, message,
				userId);
		// 实际实现中应该记录到业务日志系统
	}

	private void updateBusinessStatus(String processInstanceId, String taskKey, String status, String userId) {
		log.info("更新业务状态: processInstanceId={}, taskKey={}, status={}, user={}", processInstanceId, taskKey, status,
				userId);
		// 实际实现中应该调用业务服务更新状态
	}

	private void handleApprovalReject(String processInstanceId, String taskKey, String comment, String userId) {
		log.info("处理审批驳回: processInstanceId={}, taskKey={}, comment={}, user={}", processInstanceId, taskKey, comment,
				userId);
		// 实际实现中可以进行特殊的驳回处理逻辑
	}

}
