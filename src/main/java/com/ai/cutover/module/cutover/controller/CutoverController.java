package com.ai.cutover.module.cutover.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.ai.cutover.module.cutover.model.req.StartCutoverReq;
import com.ai.cutover.module.cutover.model.resp.*;
import com.ai.cutover.module.cutover.service.CutoverProcessService;
import com.ai.cutover.module.process.model.req.ApproveTaskReq;
import com.ai.cutover.module.process.model.req.CompleteTaskReq;
import com.ai.cutover.module.process.model.req.TerminateProcessReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.Arrays;

/**
 * 割接业务控制器 提供割接相关的业务接口
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/api/cutover")
@RequiredArgsConstructor
@Validated
public class CutoverController {

	private final CutoverProcessService cutoverProcessService;

	/**
	 * 启动割接流程
	 */
	@PostMapping("/start")
	public StartCutoverResp startCutoverProcess(@Valid @RequestBody StartCutoverReq request) {
		String currentUserId = StpUtil.getLoginIdAsString();
		return cutoverProcessService.startCutoverProcess(request, currentUserId);
	}

	/**
	 * 审批割接任务
	 */
	@PostMapping("/approve")
	public ApproveTaskResp approveCutoverTask(@Valid @RequestBody ApproveTaskReq request) {
		String currentUserId = StpUtil.getLoginIdAsString();
		return cutoverProcessService.approveCutoverTask(request.getTaskId(), currentUserId, request.getApproved(),
				request.getComment());
	}

	/**
	 * 完成割接任务
	 */
	@PostMapping("/complete")
	public CompleteTaskResp completeCutoverTask(@Valid @RequestBody CompleteTaskReq request) {
		String currentUserId = StpUtil.getLoginIdAsString();
		return cutoverProcessService.completeCutoverTask(request.getTaskId(), currentUserId, request.getVariables());
	}

	/**
	 * 终止割接流程
	 */
	@PostMapping("/terminate")
	public TerminateProcessResp terminateCutoverProcess(@Valid @RequestBody TerminateProcessReq request) {
		String currentUserId = StpUtil.getLoginIdAsString();
		return cutoverProcessService.terminateCutoverProcess(request.getProcessInstanceId(), request.getReason(),
				currentUserId);
	}

	/**
	 * 获取割接流程信息 GET /api/cutover/info
	 */
	@GetMapping("/info")
	public CutoverInfoResp getCutoverInfo() {
		return CutoverInfoResp.builder()
			.businessType("cutover")
			.description("割接管控系统")
			.supportedTypes(Arrays.asList("PLANNED", "EMERGENCY", "GENERAL"))
			.version("1.0")
			.moduleStatus("ACTIVE")
			.configInfo("割接流程管控模块已启用")
			.build();
	}

}
