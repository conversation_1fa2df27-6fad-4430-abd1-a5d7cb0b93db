package com.ai.cutover.module.cutover.model.resp;

import lombok.Builder;
import lombok.Data;

/**
 * 终止流程响应
 *
 * <AUTHOR>
 */
@Data
@Builder
public class TerminateProcessResp {

	/**
	 * 流程实例ID
	 */
	private String processInstanceId;

	/**
	 * 终止原因
	 */
	private String reason;

	/**
	 * 操作人ID
	 */
	private String operatorId;

	/**
	 * 终止时间
	 */
	private String terminationTime;

	/**
	 * 流程状态
	 */
	private String processStatus;

}
