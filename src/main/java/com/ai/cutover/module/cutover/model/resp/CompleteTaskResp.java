package com.ai.cutover.module.cutover.model.resp;

import lombok.Builder;
import lombok.Data;
import java.util.Map;

/**
 * 完成任务响应
 *
 * <AUTHOR>
 */
@Data
@Builder
public class CompleteTaskResp {

	/**
	 * 任务ID
	 */
	private String taskId;

	/**
	 * 执行人ID
	 */
	private String executorId;

	/**
	 * 完成时间
	 */
	private String completionTime;

	/**
	 * 任务变量
	 */
	private Map<String, Object> variables;

	/**
	 * 下一个任务信息
	 */
	private String nextTaskInfo;

}
