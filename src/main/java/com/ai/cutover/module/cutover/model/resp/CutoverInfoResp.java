package com.ai.cutover.module.cutover.model.resp;

import lombok.Builder;
import lombok.Data;
import java.util.List;

/**
 * 割接信息响应
 *
 * <AUTHOR>
 */
@Data
@Builder
public class CutoverInfoResp {

	/**
	 * 业务类型
	 */
	private String businessType;

	/**
	 * 系统描述
	 */
	private String description;

	/**
	 * 支持的割接类型
	 */
	private List<String> supportedTypes;

	/**
	 * 系统版本
	 */
	private String version;

	/**
	 * 模块状态
	 */
	private String moduleStatus;

	/**
	 * 配置信息
	 */
	private String configInfo;

}
