package com.ai.cutover.module.cutover.model.resp;

import lombok.Builder;
import lombok.Data;

/**
 * 启动割接流程响应
 *
 * <AUTHOR>
 */
@Data
@Builder
public class StartCutoverResp {

	/**
	 * 流程实例ID
	 */
	private String processInstanceId;

	/**
	 * 业务Key
	 */
	private Long businessKey;

	/**
	 * 割接类型
	 */
	private String cutoverType;

	/**
	 * 流程Key
	 */
	private String processKey;

	/**
	 * 发起人ID
	 */
	private String initiatorId;

	/**
	 * 启动时间
	 */
	private String startTime;

}
