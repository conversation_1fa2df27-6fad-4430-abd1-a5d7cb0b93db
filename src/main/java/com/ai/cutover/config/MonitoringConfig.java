package com.ai.cutover.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.Health;
import org.springframework.boot.actuate.health.HealthIndicator;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.ai.cutover.common.constant.CommonConstants;
import com.ai.cutover.common.constant.ErrorMessages;
import com.ai.cutover.common.exception.BusinessException;
import org.springframework.boot.actuate.info.InfoContributor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 监控配置类 配置应用监控、健康检查和应用信息
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class MonitoringConfig {

	/**
	 * 自定义应用信息贡献者
	 */
	@Bean
	public InfoContributor customInfoContributor() {
		return builder -> {
			Map<String, Object> appInfo = new HashMap<>();
			appInfo.put("name", "切换管控系统");
			appInfo.put("description", "企业级切换管控系统，提供完整的用户管理、权限控制、流程管理等功能");
			appInfo.put("version", "1.0.0");
			appInfo.put("startup-time",
					DateUtil.format(LocalDateTime.now(), CommonConstants.DateTimeFormat.YYYY_MM_DD_HH_MM_SS));

			Map<String, Object> techStack = new HashMap<>();
			techStack.put("framework", "Spring Boot 3.x");
			techStack.put("database", "MySQL 8.0 + MyBatis Flex");
			techStack.put("cache", "Redis");
			techStack.put("security", "SaToken");
			techStack.put("process-engine", "Camunda");

			Map<String, Object> features = new HashMap<>();
			features.put("user-management", "用户管理");
			features.put("role-management", "角色管理");
			features.put("permission-management", "权限管理");
			features.put("process-management", "流程管理");
			features.put("cutover-management", "切换管控");

			builder.withDetail("application", appInfo);
			builder.withDetail("tech-stack", techStack);
			builder.withDetail("features", features);
		};
	}

	/**
	 * 自定义健康检查指示器
	 */
	@Bean
	public HealthIndicator customHealthIndicator() {
		return () -> {
			try {
				// 检查应用核心功能
				boolean isHealthy = checkApplicationHealth();

				Map<String, Object> details = new HashMap<>();
				details.put("timestamp",
						DateUtil.format(LocalDateTime.now(), CommonConstants.DateTimeFormat.YYYY_MM_DD_HH_MM_SS));
				details.put("uptime", getUptime());
				details.put("memory-usage", getMemoryUsage());

				if (isHealthy) {
					details.put("status", "应用运行正常");
					return Health.up().withDetails(details).build();
				}
				else {
					details.put("status", "应用运行异常");
					return Health.down().withDetails(details).build();
				}
			}
			catch (Exception e) {
				log.error("健康检查失败", e);
				return Health.down()
					.withDetail("status", "健康检查失败")
					.withDetail("error", e.getMessage())
					.withDetail("timestamp",
							DateUtil.format(LocalDateTime.now(), CommonConstants.DateTimeFormat.YYYY_MM_DD_HH_MM_SS))
					.build();
			}
		};
	}

	/**
	 * 检查应用健康状态
	 */
	private boolean checkApplicationHealth() {
		try {
			// 检查内存使用情况
			Runtime runtime = Runtime.getRuntime();
			long maxMemory = runtime.maxMemory();
			long totalMemory = runtime.totalMemory();
			long freeMemory = runtime.freeMemory();
			long usedMemory = totalMemory - freeMemory;

			// 如果内存使用率超过90%，认为不健康
			double memoryUsageRatio = (double) usedMemory / maxMemory;
			if (memoryUsageRatio > 0.9) {
				log.warn("内存使用率过高: {:.2f}%", memoryUsageRatio * 100);
				return false;
			}

			return true;
		}
		catch (Exception e) {
			log.error("健康检查异常", e);
			return false;
		}
	}

	/**
	 * 获取应用运行时间
	 */
	private String getUptime() {
		long uptimeMillis = System.currentTimeMillis() - getStartTime();
		long seconds = uptimeMillis / 1000;
		long minutes = seconds / 60;
		long hours = minutes / 60;
		long days = hours / 24;

		if (days > 0) {
			return StrUtil.format(CommonConstants.FormatConstants.TIME_FORMAT_DAYS_HOURS_MINUTES, days, hours % 24,
					minutes % 60);
		}
		else if (hours > 0) {
			return StrUtil.format(CommonConstants.FormatConstants.TIME_FORMAT_HOURS_MINUTES, hours, minutes % 60);
		}
		else if (minutes > 0) {
			return StrUtil.format(CommonConstants.FormatConstants.TIME_FORMAT_MINUTES_SECONDS, minutes, 0);
		}
		else {
			return StrUtil.format(CommonConstants.FormatConstants.TIME_FORMAT_SECONDS, seconds);
		}
	}

	/**
	 * 获取内存使用情况
	 */
	private Map<String, String> getMemoryUsage() {
		Runtime runtime = Runtime.getRuntime();
		long maxMemory = runtime.maxMemory();
		long totalMemory = runtime.totalMemory();
		long freeMemory = runtime.freeMemory();
		long usedMemory = totalMemory - freeMemory;

		Map<String, String> memoryInfo = new HashMap<>();
		memoryInfo.put("max", formatBytes(maxMemory));
		memoryInfo.put("total", formatBytes(totalMemory));
		memoryInfo.put("used", formatBytes(usedMemory));
		memoryInfo.put("free", formatBytes(freeMemory));
		memoryInfo.put("usage-ratio", String.format("%.2f%%", (double) usedMemory / maxMemory * 100));

		return memoryInfo;
	}

	/**
	 * 格式化字节数
	 */
	private String formatBytes(long bytes) {
		if (bytes < 1024)
			return bytes + " B";
		int exp = (int) (Math.log(bytes) / Math.log(1024));
		String pre = "KMGTPE".charAt(exp - 1) + "";
		return String.format("%.2f %sB", bytes / Math.pow(1024, exp), pre);
	}

	/**
	 * 获取应用启动时间（简化实现）
	 */
	private long getStartTime() {
		// 简化实现，可以通过ApplicationContext获取真实启动时间
		return System.currentTimeMillis() - 300000; // 假设已运行5分钟
	}

}
