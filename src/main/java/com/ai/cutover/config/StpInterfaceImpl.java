package com.ai.cutover.config;

import cn.dev33.satoken.stp.StpInterface;
import com.ai.cutover.module.system.constant.RoleConstants;
import com.ai.cutover.module.system.model.dto.SysUserDTO;
import com.ai.cutover.module.system.service.SysUserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * 自定义权限验证接口扩展
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class StpInterfaceImpl implements StpInterface {

	private final SysUserService sysUserService;

	/**
	 * 返回一个账号所拥有的权限码集合
	 */
	@Override
	public List<String> getPermissionList(Object loginId, String loginType) {
		try {
			Long userId = Long.valueOf(loginId.toString());
			Set<String> permissions = sysUserService.getUserPermissions(userId);
			log.debug("获取用户权限: userId={}, permissions={}", userId, permissions);
			return permissions != null ? permissions.stream().toList() : new ArrayList<>();
		}
		catch (Exception e) {
			log.error("获取用户权限列表失败: loginId={}", loginId, e);
			return new ArrayList<>();
		}
	}

	/**
	 * 返回一个账号所拥有的角色标识集合 (权限与角色可分开校验)
	 */
	@Override
	public List<String> getRoleList(Object loginId, String loginType) {
		try {
			Long userId = Long.valueOf(loginId.toString());

			// 获取用户信息
			SysUserDTO user = sysUserService.getUserById(userId);
			if (user == null) {
				log.warn("用户不存在: userId={}", userId);
				return new ArrayList<>();
			}

			// 获取用户角色
			Set<String> roleSet = sysUserService.getUserRoles(userId);
			List<String> roles = roleSet != null ? roleSet.stream().toList() : new ArrayList<>();

			// 检查是否为内置用户，添加特殊角色
			if ("Y".equals(user.getBuiltinFlag())) {
				if ("superadmin".equals(user.getUsername())) {
					if (!roles.contains(RoleConstants.SUPER_ADMIN)) {
						roles = new ArrayList<>(roles);
						roles.add(RoleConstants.SUPER_ADMIN);
					}
				}
				else if ("admin".equals(user.getUsername())) {
					if (!roles.contains(RoleConstants.ADMIN)) {
						roles = new ArrayList<>(roles);
						roles.add(RoleConstants.ADMIN);
					}
				}
			}

			log.debug("获取用户角色: userId={}, roles={}", userId, roles);
			return roles;
		}
		catch (Exception e) {
			log.error("获取用户角色列表失败: loginId={}", loginId, e);
			return new ArrayList<>();
		}
	}

}
