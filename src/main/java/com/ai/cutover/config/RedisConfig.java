package com.ai.cutover.config;

import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.cache.RedisCacheConfiguration;
import org.springframework.data.redis.cache.RedisCacheManager;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.RedisSerializationContext;
import org.springframework.data.redis.serializer.StringRedisSerializer;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;

/**
 * Redis配置类 用于配置RedisTemplate和RedisCacheManager
 *
 * <AUTHOR>
 */
@Configuration
@EnableCaching
public class RedisConfig {

	/**
	 * 配置RedisTemplate
	 */
	@Bean
	public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
		RedisTemplate<String, Object> template = new RedisTemplate<>();
		template.setConnectionFactory(factory);

		// 配置序列化器
		StringRedisSerializer stringSerializer = new StringRedisSerializer();
		GenericJackson2JsonRedisSerializer jsonSerializer = new GenericJackson2JsonRedisSerializer();

		// 设置key的序列化器
		template.setKeySerializer(stringSerializer);
		template.setHashKeySerializer(stringSerializer);

		// 设置value的序列化器
		template.setValueSerializer(jsonSerializer);
		template.setHashValueSerializer(jsonSerializer);

		template.afterPropertiesSet();
		return template;
	}

	// /**
	// * 配置缓存管理器
	// */
	// @Bean
	// public RedisCacheManager cacheManager(RedisConnectionFactory factory) {
	// RedisCacheConfiguration defaultCacheConfig =
	// RedisCacheConfiguration.defaultCacheConfig()
	// .entryTtl(Duration.ofMinutes(10))
	// .serializeKeysWith(RedisSerializationContext.SerializationPair.fromSerializer(new
	// StringRedisSerializer()))
	// .serializeValuesWith(RedisSerializationContext.SerializationPair
	// .fromSerializer(new GenericJackson2JsonRedisSerializer()))
	// .disableCachingNullValues();
	//
	// return
	// RedisCacheManager.builder(factory).cacheDefaults(defaultCacheConfig).build();
	// }

}
