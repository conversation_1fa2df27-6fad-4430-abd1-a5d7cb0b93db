package com.ai.cutover.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * SaToken配置类
 *
 * <AUTHOR>
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

	/**
	 * 注册Sa-Token拦截器，打开注解式鉴权功能
	 */
	@Override
	public void addInterceptors(InterceptorRegistry registry) {
		registry.addInterceptor(new SaInterceptor(handler -> {
			SaRouter.match("/**")
				.notMatch("/api/system/auth/login")
				.notMatch("/api/system/auth/captcha")
				.notMatch("/api/system/auth/verify-captcha")
				.notMatch("/api/test/**")
				.notMatch("/camunda/**")
				.notMatch("/actuator/**")
				.notMatch("/error")
				.notMatch("/favicon.ico")
				.notMatch("/doc.html")
				.notMatch("/webjars/**")
				.notMatch("/v3/api-docs/**")
				.notMatch("/swagger-resources/**")
				// 执行认证方法
				.check(r -> StpUtil.checkLogin());
		})).addPathPatterns("/**");
	}

}
