package com.ai.cutover.config;

import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Knife4j配置类 用于配置Knife4j的文档信息
 *
 * <AUTHOR>
 */
@Configuration
public class Knife4jConfig {

	//
	// @Bean
	// public GroupedOpenApi systemApi() {
	// return
	// GroupedOpenApi.builder().group("系统模块").pathsToMatch("/api/system/**").build();
	// }
	//
	// @Bean
	// public GroupedOpenApi cutoverApi() {
	// return
	// GroupedOpenApi.builder().group("割接模块").pathsToMatch("/api/cutover/**").build();
	// }
	//
	// @Bean
	// public GroupedOpenApi processApi() {
	// return
	// GroupedOpenApi.builder().group("流程模块").pathsToMatch("/api/process/**").build();
	// }

}
