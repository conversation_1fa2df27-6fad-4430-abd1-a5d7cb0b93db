package com.ai.cutover.config;

import com.mybatisflex.spring.FlexTransactionFactory;
import org.apache.ibatis.session.SqlSessionFactory;
import org.camunda.bpm.engine.ProcessEngineConfiguration;
import org.camunda.bpm.engine.impl.cfg.ProcessEngineConfigurationImpl;
import org.camunda.bpm.engine.impl.history.HistoryLevel;
import org.camunda.bpm.engine.spring.SpringProcessEngineConfiguration;
import org.camunda.bpm.spring.boot.starter.util.CamundaSpringBootUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.PlatformTransactionManager;

import javax.sql.DataSource;
import java.lang.reflect.Field;

/**
 * Camunda流程引擎配置类
 *
 * <AUTHOR>
 */
@Configuration
public class CamundaProcessConfig {

	@Bean
	public ProcessEngineConfigurationImpl processEngineConfiguration(SqlSessionFactory sqlSessionFactory,
			PlatformTransactionManager annotationDrivenTransactionManager)
			throws NoSuchFieldException, IllegalAccessException {
		final SpringProcessEngineConfiguration processEngineConfiguration = CamundaSpringBootUtil
			.springProcessEngineConfiguration();
		DataSource dataSource = sqlSessionFactory.getConfiguration().getEnvironment().getDataSource();
		Field field = ProcessEngineConfiguration.class.getDeclaredField("dataSource");
		field.setAccessible(true);
		field.set(processEngineConfiguration, dataSource);
		processEngineConfiguration.setTransactionManager(annotationDrivenTransactionManager);
		processEngineConfiguration.setDatabaseSchemaUpdate(ProcessEngineConfiguration.DB_SCHEMA_UPDATE_FALSE);
		processEngineConfiguration.setTransactionFactory(new FlexTransactionFactory());
		processEngineConfiguration.setHistoryLevel(HistoryLevel.HISTORY_LEVEL_FULL);
		processEngineConfiguration.setEnforceHistoryTimeToLive(false);
		processEngineConfiguration.setJobExecutorActivate(false);
		return processEngineConfiguration;
	}

}
