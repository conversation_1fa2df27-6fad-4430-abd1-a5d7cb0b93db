2025-07-25 09:07:38.527 [main] INFO  c.ai.cutover.module.system.service.AuthServiceTest - Starting AuthServiceTest using Java 17.0.13 with PID 22484 (started by <PERSON><PERSON><PERSON> in F:\dev\project\my\camunda)
2025-07-25 09:07:38.534 [main] INFO  c.ai.cutover.module.system.service.AuthServiceTest - The following 1 profile is active: "test"
2025-07-25 09:07:39.859 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 09:07:39.862 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 09:07:39.932 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 44 ms. Found 0 Redis repository interfaces.
2025-07-25 09:07:42.223 [main] INFO  org.camunda.bpm.spring.boot - STARTER-SB040 Setting up jobExecutor with corePoolSize=3, maxPoolSize:10
2025-07-25 09:07:42.372 [main] INFO  org.camunda.bpm.engine.cfg - ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaJobConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, CreateAdminUserConfiguration[adminUser=AdminUserProperty[id=admin, firstName=Admin, lastName=User, email=<EMAIL>, password=******]], failedJobConfiguration, CreateFilterConfiguration[filterName=All tasks], eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-07-25 09:07:42.409 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin - EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-07-25 09:07:42.409 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin - EVENTING-003: Task events will be published as Spring Events.
2025-07-25 09:07:42.409 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin - EVENTING-005: Execution events will be published as Spring Events.
2025-07-25 09:07:42.409 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin - EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-07-25 09:07:42.425 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin - EVENTING-007: History events will be published as Spring events.
2025-07-25 09:07:42.723 [main] INFO  org.camunda.feel.FeelEngine - Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@27746c5e)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@50ac1249, clock: SystemClock, configuration: Configuration(false)]
2025-07-25 09:07:44.831 [main] INFO  org.camunda.bpm.connect - CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-07-25 09:07:44.833 [main] INFO  org.camunda.bpm.connect - CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-07-25 09:07:44.858 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-25 09:07:45.029 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@1f5f4d48
2025-07-25 09:07:45.033 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-25 09:07:45.227 [main] INFO  org.camunda.bpm.engine - ENGINE-00001 Process Engine default created.
2025-07-25 09:07:45.290 [main] INFO  org.camunda.bpm.spring.boot - STARTER-SB011 Skip creating initial Admin User, user does exist: UserEntity[id=admin, revision=1, firstName=Admin, lastName=Admin, email=admin@localhost, password=******, salt=******, lockExpirationTime=null, attempts=0]
2025-07-25 09:07:45.307 [main] INFO  org.camunda.bpm.spring.boot - STARTER-SB016 Skip initial filter creation, the filter with this name already exists: All tasks
2025-07-25 09:07:47.025 [main] WARN  o.s.web.servlet.resource.ResourceHandlerUtils - Appended trailing slash to static resource location: classpath:/META-INF/resources/webjars/camunda/
2025-07-25 09:07:47.267 [main] INFO  c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.web.context.support.GenericWebApplicationContext@70e02081
2025-07-25 09:07:47.695 [main] INFO  c.ai.cutover.module.system.service.AuthServiceTest - Started AuthServiceTest in 9.718 seconds (process running for 11.166)
2025-07-25 09:07:47.704 [main] INFO  org.camunda.bpm.engine.jobexecutor - ENGINE-14014 Starting up the JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor].
2025-07-25 09:07:47.706 [JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor]] INFO  org.camunda.bpm.engine.jobexecutor - ENGINE-14018 JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor] starting to acquire jobs
2025-07-25 09:07:48.856 [SpringApplicationShutdownHook] INFO  org.camunda.bpm.engine.jobexecutor - ENGINE-14015 Shutting down the JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor]
2025-07-25 09:07:48.857 [JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor]] INFO  org.camunda.bpm.engine.jobexecutor - ENGINE-14020 JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor] stopped job acquisition
2025-07-25 09:07:48.859 [SpringApplicationShutdownHook] INFO  org.camunda.bpm.engine - ENGINE-00007 Process Engine default closed
2025-07-25 09:07:48.861 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-25 09:07:48.869 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
2025-07-25 09:07:52.740 [main] INFO  c.ai.cutover.module.system.service.AuthServiceTest - Starting AuthServiceTest using Java 17.0.13 with PID 10492 (started by arnan in F:\dev\project\my\camunda)
2025-07-25 09:07:52.742 [main] INFO  c.ai.cutover.module.system.service.AuthServiceTest - The following 1 profile is active: "test"
2025-07-25 09:07:54.212 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Multiple Spring Data modules found, entering strict repository configuration mode
2025-07-25 09:07:54.216 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-07-25 09:07:54.296 [main] INFO  o.s.d.r.config.RepositoryConfigurationDelegate - Finished Spring Data repository scanning in 55 ms. Found 0 Redis repository interfaces.
2025-07-25 09:07:56.680 [main] INFO  org.camunda.bpm.spring.boot - STARTER-SB040 Setting up jobExecutor with corePoolSize=3, maxPoolSize:10
2025-07-25 09:07:56.813 [main] INFO  org.camunda.bpm.engine.cfg - ENGINE-12003 Plugin 'CompositeProcessEnginePlugin[genericPropertiesConfiguration, camundaProcessEngineConfiguration, camundaDatasourceConfiguration, camundaJobConfiguration, camundaHistoryConfiguration, camundaMetricsConfiguration, camundaAuthorizationConfiguration, camundaDeploymentConfiguration, CreateAdminUserConfiguration[adminUser=AdminUserProperty[id=admin, firstName=Admin, lastName=User, email=<EMAIL>, password=******]], failedJobConfiguration, CreateFilterConfiguration[filterName=All tasks], eventPublisherPlugin, ApplicationContextClassloaderSwitchPlugin]' activated on process engine 'default'
2025-07-25 09:07:56.845 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin - EVENTING-001: Initialized Camunda Spring Boot Eventing Engine Plugin.
2025-07-25 09:07:56.846 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin - EVENTING-003: Task events will be published as Spring Events.
2025-07-25 09:07:56.846 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin - EVENTING-005: Execution events will be published as Spring Events.
2025-07-25 09:07:56.846 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin - EVENTING-009: Listeners will not be invoked if a skipCustomListeners API parameter is set to true by user.
2025-07-25 09:07:56.856 [main] INFO  o.c.b.s.boot.starter.event.EventPublisherPlugin - EVENTING-007: History events will be published as Spring events.
2025-07-25 09:07:57.157 [main] INFO  org.camunda.feel.FeelEngine - Engine created. [value-mapper: CompositeValueMapper(List(org.camunda.feel.impl.JavaValueMapper@a6fc1bc)), function-provider: org.camunda.bpm.dmn.feel.impl.scala.function.CustomFunctionTransformer@d190639, clock: SystemClock, configuration: Configuration(false)]
2025-07-25 09:07:59.637 [main] INFO  org.camunda.bpm.connect - CNCT-01004 Discovered provider for connector id 'http-connector' and class 'org.camunda.connect.httpclient.impl.HttpConnectorImpl': 'org.camunda.connect.httpclient.impl.HttpConnectorProviderImpl'
2025-07-25 09:07:59.640 [main] INFO  org.camunda.bpm.connect - CNCT-01004 Discovered provider for connector id 'soap-http-connector' and class 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorImpl': 'org.camunda.connect.httpclient.soap.impl.SoapHttpConnectorProviderImpl'
2025-07-25 09:07:59.671 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Starting...
2025-07-25 09:07:59.894 [main] INFO  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Added connection com.mysql.cj.jdbc.ConnectionImpl@7d44eab
2025-07-25 09:07:59.900 [main] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Start completed.
2025-07-25 09:08:00.220 [main] INFO  org.camunda.bpm.engine - ENGINE-00001 Process Engine default created.
2025-07-25 09:08:00.296 [main] INFO  org.camunda.bpm.spring.boot - STARTER-SB011 Skip creating initial Admin User, user does exist: UserEntity[id=admin, revision=1, firstName=Admin, lastName=Admin, email=admin@localhost, password=******, salt=******, lockExpirationTime=null, attempts=0]
2025-07-25 09:08:00.318 [main] INFO  org.camunda.bpm.spring.boot - STARTER-SB016 Skip initial filter creation, the filter with this name already exists: All tasks
2025-07-25 09:08:03.099 [main] WARN  o.s.web.servlet.resource.ResourceHandlerUtils - Appended trailing slash to static resource location: classpath:/META-INF/resources/webjars/camunda/
2025-07-25 09:08:03.484 [main] INFO  c.b.m.e.spring.MybatisPlusApplicationContextAware - Register ApplicationContext instances org.springframework.web.context.support.GenericWebApplicationContext@646811d6
2025-07-25 09:08:03.970 [main] INFO  c.ai.cutover.module.system.service.AuthServiceTest - Started AuthServiceTest in 12.016 seconds (process running for 13.66)
2025-07-25 09:08:03.977 [main] INFO  org.camunda.bpm.engine.jobexecutor - ENGINE-14014 Starting up the JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor].
2025-07-25 09:08:03.980 [JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor]] INFO  org.camunda.bpm.engine.jobexecutor - ENGINE-14018 JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor] starting to acquire jobs
2025-07-25 09:08:04.049 [SpringApplicationShutdownHook] INFO  org.camunda.bpm.engine.jobexecutor - ENGINE-14015 Shutting down the JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor]
2025-07-25 09:08:04.049 [JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor]] INFO  org.camunda.bpm.engine.jobexecutor - ENGINE-14020 JobExecutor[org.camunda.bpm.engine.spring.components.jobexecutor.SpringJobExecutor] stopped job acquisition
2025-07-25 09:08:04.052 [SpringApplicationShutdownHook] INFO  org.camunda.bpm.engine - ENGINE-00007 Process Engine default closed
2025-07-25 09:08:04.054 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown initiated...
2025-07-25 09:08:04.064 [SpringApplicationShutdownHook] INFO  com.zaxxer.hikari.HikariDataSource - HikariPool-1 - Shutdown completed.
