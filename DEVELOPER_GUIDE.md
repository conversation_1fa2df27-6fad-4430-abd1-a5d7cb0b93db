# 开发者指南

## 📋 项目概述

本项目是一个基于 Spring Boot 3 的企业级切换管控系统，集成了用户管理、权限控制、流程管理等核心功能。

### 技术栈
- **框架**: Spring Boot 3.x + Spring Security
- **数据库**: MyBatis Flex
- **权限**: SaToken
- **缓存**: Redis
- **流程引擎**: Camunda
- **工具库**: Hutool + MapStruct Plus
- **构建工具**: Maven

## 🚀 快速开始

### 1. 环境要求
- JDK 17+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 2. 项目启动
```bash
# 克隆项目
git clone <repository-url>

# 编译项目（生成 APT 表定义类）
mvn clean compile

# 启动应用
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 3. 访问地址
- API文档: http://localhost:10000/doc.html
- 健康检查: http://localhost:10000/actuator/health

### 4. 默认账户
| 用户名 | 密码 | 角色 |
|--------|------|------|
| superadmin | 123456 | 超级管理员 |
| admin | 123456 | 系统管理员 |

## 📁 项目结构

```
src/main/java/com/ai/cutover/
├── common/                 # 公共模块
│   ├── config/            # 配置类
│   ├── constant/          # 常量定义
│   ├── enums/             # 枚举类型
│   ├── exception/         # 异常处理
│   ├── service/           # 公共服务
│   └── util/              # 工具类
├── module/                # 业务模块
│   ├── system/            # 系统管理模块
│   ├── process/           # 流程管理模块
│   └── cutover/           # 切换管控模块
└── CutoverControlApplication.java
```

## 📝 代码规范

### 1. 命名规范

#### 类命名
- **Controller**: 使用 `XxxController` 后缀
- **Service**: 接口使用 `XxxService`，实现类使用 `XxxServiceImpl`
- **DAO**: 使用 `XxxDao` 后缀
- **Entity**: 实体类名与数据库表名对应
- **DTO**: 使用 `XxxDTO` 后缀
- **Request**: 使用 `XxxReq` 后缀
- **Response**: 使用 `XxxResp` 后缀

#### 方法命名
- **查询单个**: `getXxx()`
- **查询列表**: `getXxxList()`
- **查询分页**: `getXxxPage()`
- **统计数量**: `countXxx()`
- **插入数据**: `save()` 或 `insert()`
- **删除数据**: `remove()` 或 `delete()`
- **修改数据**: `update()`

#### 变量命名
- **布尔类型**: 使用 `xxxFlag` 命名
- **常量**: 使用 `UPPER_SNAKE_CASE`
- **变量**: 使用 `camelCase`

### 2. 注解规范

#### Controller 层
```java
@RestController
@RequestMapping("/api/system/user")
@RequiredArgsConstructor
@Slf4j
public class SysUserController {
    
    @GetMapping("/page")
    @SaCheckPermission(PermissionConstants.User.VIEW)
    public Page<SysUserResp> getUserPage(UserQueryReq request) {
        // 实现逻辑
    }
    
    @PostMapping
    @SaCheckPermission(PermissionConstants.User.CREATE)
    public void createUser(@Valid @RequestBody CreateUserReq request) {
        // 实现逻辑
    }
}
```

#### Service 层
```java
@Service
@RequiredArgsConstructor
@Slf4j
@Transactional(rollbackFor = Exception.class)
public class SysUserServiceImpl implements SysUserService {
    
    @Override
    public void createUser(CreateUserReq request) {
        // 实现逻辑
    }
}
```

#### Entity 层
```java
@Data
@Table("sys_user")
public class SysUser {
    
    @Id
    @KeyType(KeyType.Auto)
    private Long id;
    
    @Column("username")
    private String username;
    
    // 其他字段...
}
```

### 3. 数据库操作规范

#### 使用 MyBatis Flex
```java
// ✅ 推荐：使用 QueryChain
List<SysUser> users = QueryChain.of(sysUserDao)
    .where(SysUser::getStatus).eq("NORMAL")
    .and(SysUser::getDeptId).eq(deptId)
    .list();

// ✅ 推荐：使用 .listAs() 直接转换
List<SysUserDTO> userDTOs = QueryChain.of(sysUserDao)
    .where(SysUser::getStatus).eq("NORMAL")
    .listAs(SysUserDTO.class);

// ✅ 推荐：使用 .paginateAs() 分页转换
Page<SysUserDTO> userPage = sysUserDao.paginateAs(
    pageNum, pageSize, wrapper, SysUserDTO.class);
```

#### 避免 N+1 查询
```java
// ❌ 避免：N+1 查询
userPage.map(user -> {
    SysDept dept = sysDeptDao.selectOneById(user.getDeptId()); // N次查询
    // ...
});

// ✅ 推荐：批量查询
Map<Long, String> deptNameMap = CacheUtil.batchQueryToMap(
    deptIds, sysDeptDao::selectListByIds, SysDept::getId, SysDept::getDeptName);
```

### 4. 异常处理规范

#### 使用 Assert 工具类
```java
// ✅ 推荐：使用枚举类型
Assert.resourceExists(user, ResourceType.USER);
Assert.uniqueConstraint(exists, UniqueFieldType.USERNAME, username);
Assert.hasPermission(hasPermission, OperationType.DELETE);

// ❌ 避免：手动抛出异常
if (user == null) {
    throw new BusinessException("用户不存在");
}
```

#### 全局异常处理
项目已配置全局异常处理器，业务代码中只需抛出 `BusinessException`。

### 5. 常量管理规范

#### 按功能分类常量
```java
// ✅ 推荐：按功能分类
public static final class UserStatus {
    public static final String NORMAL = "NORMAL";
    public static final String DISABLED = "DISABLED";
}

public static final class CacheKey {
    public static final String USER_PREFIX = "user:";
    public static final String ROLE_PREFIX = "role:";
}

// ❌ 避免：所有常量放在一个类中
public static final class Constants {
    public static final String USER_NORMAL = "NORMAL";
    public static final String CACHE_USER = "user:";
    // ... 混杂的常量
}
```

### 6. 日志规范

```java
// ✅ 推荐：结构化日志
log.info("用户登录成功: username={}, ip={}", username, loginIp);
log.warn("用户登录失败: username={}, reason={}", username, reason);
log.error("系统异常: method={}, params={}", methodName, params, exception);

// ❌ 避免：字符串拼接
log.info("用户" + username + "登录成功");
```

## 🔧 开发工具配置

### 1. IDEA 配置
- 安装 Lombok 插件
- 安装 MapStruct 插件
- 配置代码格式化规则

### 2. Maven 配置
项目使用 Maven 进行依赖管理，关键配置：
- 注解处理器路径配置（Lombok、MapStruct Plus、MyBatis Flex）
- 编译插件配置

## 📚 核心功能模块

### 1. 系统管理模块
- 用户管理：用户CRUD、角色分配、密码管理
- 角色管理：角色CRUD、权限分配
- 权限管理：权限CRUD、菜单管理
- 部门管理：部门树形结构管理

### 2. 流程管理模块
- 流程定义：基于 Camunda 的流程设计
- 流程实例：流程启动、监控、管理
- 任务管理：任务分配、审批、完成

### 3. 切换管控模块
- 切换计划：切换方案设计和管理
- 切换执行：自动化切换流程
- 监控告警：实时监控和异常处理

## 🧪 测试规范

### 1. 单元测试
- 使用 JUnit 5 + Mockito
- Service 层必须编写单元测试
- 测试覆盖率要求 > 80%

### 2. 集成测试
- 使用 @SpringBootTest
- 测试完整的业务流程
- 使用 TestContainers 进行数据库测试

## 📦 部署规范

### 1. 环境配置
- 开发环境：application-dev.yml
- 测试环境：application-test.yml
- 生产环境：application-prod.yml

### 2. 构建部署
```bash
# 构建
mvn clean package -Dmaven.test.skip=true

# Docker 部署
docker build -t cutover-control .
docker run -d -p 10000:10000 cutover-control
```

## 🔍 常见问题

### 1. MyBatis Flex 相关
- **问题**: 表定义类未生成
- **解决**: 执行 `mvn clean compile` 生成 APT 类

### 2. 权限相关
- **问题**: 接口无权限访问
- **解决**: 检查 @SaCheckPermission 注解配置

### 3. 缓存相关
- **问题**: Redis 连接失败
- **解决**: 检查 Redis 配置和网络连接

## 🛠️ 工具类使用指南

### 1. Assert 断言工具
```java
// 资源存在性检查
Assert.resourceExists(user, ResourceType.USER);
Assert.resourceNotExists(existingUser, ResourceType.USER);

// 唯一性约束检查
Assert.uniqueConstraint(exists, UniqueFieldType.USERNAME, username);
Assert.uniqueConstraint(exists, UniqueFieldType.EMAIL, email);

// 权限检查
Assert.hasPermission(hasPermission, OperationType.CREATE);
Assert.hasPermission(canDelete, OperationType.DELETE);

// 状态检查
Assert.validStatus(status, "NORMAL", ResourceType.USER);
Assert.userStatusNormal(userStatus);
```

### 2. CacheUtil 缓存工具
```java
// 批量查询优化
Map<Long, String> deptNameMap = CacheUtil.batchQueryToMap(
    deptIds,                    // ID集合
    sysDeptDao::selectListByIds, // 批量查询函数
    SysDept::getId,             // 键提取器
    SysDept::getDeptName        // 值提取器
);

// 批量查询实体映射
Map<Long, SysDept> deptMap = CacheUtil.batchQueryToEntityMap(
    deptIds, sysDeptDao::selectListByIds, SysDept::getId);
```

### 3. PerformanceMonitor 性能监控
```java
// 监控方法执行时间
String result = PerformanceMonitor.monitor("业务操作", () -> {
    return businessService.doSomething();
});

// 监控无返回值方法
PerformanceMonitor.monitor("数据处理", () -> {
    dataService.processData();
});
```

## 📋 API 设计规范

### 1. RESTful API 设计
```java
// ✅ 推荐的 API 设计
@GetMapping("/users")           // 查询用户列表
@GetMapping("/users/{id}")      // 查询单个用户
@PostMapping("/users")          // 创建用户
@PostMapping("/users/{id}")      // 更新用户
@PostMapping("/users/{id}")   // 删除用户
@PostMapping("/users/page")      // 分页查询用户
```

### 2. 统一响应格式
```java
// 成功响应
{
    "code": 200,
    "message": "操作成功",
    "data": { ... },
    "timestamp": "2025-01-26T10:30:00"
}

// 错误响应
{
    "code": 400,
    "message": "参数错误",
    "data": null,
    "timestamp": "2025-01-26T10:30:00"
}
```

### 3. 分页响应格式
```java
{
    "code": 200,
    "message": "查询成功",
    "data": {
        "records": [...],      // 数据列表
        "pageNumber": 1,       // 当前页码
        "pageSize": 10,        // 每页大小
        "totalRow": 100,       // 总记录数
        "totalPage": 10        // 总页数
    }
}
```

## 🔐 安全规范

### 1. 权限控制
- 所有 API 接口必须添加权限注解
- 使用 SaToken 进行权限验证
- 敏感操作需要二次验证

### 2. 数据验证
```java
// 使用 Spring Validation
public class CreateUserReq {
    @NotBlank(message = "用户名不能为空")
    @Length(min = 3, max = 20, message = "用户名长度必须在3-20之间")
    private String username;

    @NotBlank(message = "密码不能为空")
    @Pattern(regexp = "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)[a-zA-Z\\d]{8,}$",
             message = "密码必须包含大小写字母和数字，长度至少8位")
    private String password;
}
```

### 3. SQL 注入防护
- 使用 MyBatis Flex 的参数化查询
- 禁止字符串拼接 SQL
- 对用户输入进行严格验证

## 📊 监控和日志

### 1. 应用监控

#### Actuator 端点
项目已集成 Spring Boot Actuator，提供以下监控端点：

| 端点 | 路径 | 说明 |
|------|------|------|
| 健康检查 | `/actuator/health` | 应用健康状态 |
| 应用信息 | `/actuator/info` | 应用基本信息 |
| 指标数据 | `/actuator/metrics` | 应用运行指标 |
| 环境信息 | `/actuator/env` | 环境变量和配置 |
| 日志配置 | `/actuator/loggers` | 日志级别管理 |

#### 自定义健康检查
```java
@Component
public class CustomHealthIndicator implements HealthIndicator {
    @Override
    public Health health() {
        // 自定义健康检查逻辑
        return Health.up()
            .withDetail("status", "应用运行正常")
            .withDetail("timestamp", LocalDateTime.now())
            .build();
    }
}
```

#### 自定义应用信息
```java
@Component
public class CustomInfoContributor implements InfoContributor {
    @Override
    public void contribute(Info.Builder builder) {
        builder.withDetail("app", Map.of(
            "name", "切换管控系统",
            "version", "1.0.0"
        ));
    }
}
```

### 2. 指标收集

#### Micrometer 集成
- 自动收集 JVM 指标（内存、GC、线程等）
- HTTP 请求指标（响应时间、状态码分布等）
- 数据库连接池指标
- 自定义业务指标

#### 自定义指标示例
```java
@Component
public class BusinessMetrics {
    private final Counter userLoginCounter;
    private final Timer requestTimer;

    public BusinessMetrics(MeterRegistry meterRegistry) {
        this.userLoginCounter = Counter.builder("user.login.count")
            .description("用户登录次数")
            .register(meterRegistry);

        this.requestTimer = Timer.builder("business.request.duration")
            .description("业务请求耗时")
            .register(meterRegistry);
    }
}
```

### 3. 日志配置

#### 日志级别配置
```yaml
logging:
  level:
    root: INFO
    com.ai.cutover: DEBUG
    com.mybatisflex: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/cutover-control.log
    max-size: 100MB
    max-history: 30
```

#### 结构化日志
```java
// 使用参数化日志
log.info("用户登录: username={}, ip={}, result={}", username, ip, result);

// 记录操作日志
log.info("用户操作: userId={}, operation={}, resource={}, result={}",
    userId, operation, resource, result);

// 记录性能日志
log.debug("方法执行: method={}, duration={}ms", methodName, duration);
```

## 🚨 注意事项

### 1. 性能优化
- 避免 N+1 查询问题
- 合理使用缓存
- 数据库索引优化
- 分页查询必须限制最大条数

### 2. 内存管理
- 大数据量处理使用流式处理
- 及时释放资源
- 避免内存泄漏

### 3. 并发安全
- 使用线程安全的集合类
- 合理使用锁机制
- 避免死锁问题

---

**文档版本**: v1.0
**更新时间**: 2025-01-26
**维护者**: 开发团队
