# 项目结构说明

## 📁 核心目录结构

```
cutover-control/
├── src/main/java/com/ai/cutover/
│   ├── common/                     # 公共模块
│   │   ├── assertion/             # 断言工具
│   │   ├── constant/              # 公共常量
│   │   ├── exception/             # 异常处理
│   │   ├── result/                # 统一返回结果
│   │   ├── service/               # 公共服务（Redis等）
│   │   └── util/                  # 工具类
│   ├── config/                    # 配置类
│   │   ├── SaTokenConfig.java     # SaToken权限配置
│   │   ├── RedisConfig.java       # Redis配置
│   │   └── StpInterfaceImpl.java  # 权限接口实现
│   └── module/system/             # 系统管理模块
│       ├── constant/              # 权限和角色常量
│       ├── controller/            # 控制器层
│       ├── service/               # 服务层
│       ├── dao/                   # 数据访问层
│       ├── model/                 # 数据模型
│       └── util/                  # 模块工具类
├── src/main/resources/
│   ├── sql/                       # 数据库脚本
│   │   ├── sys_init.sql    # 完整系统初始化脚本（雪花算法ID）
│   │   ├── quick_test_script.sql       # 快速测试验证脚本
│   │   └── test_snowflake_id.sql       # 雪花算法ID测试脚本
│   └── application*.yaml         # 配置文件
├── docs/                          # 文档目录
│   ├── README.md                  # 文档中心
│   └── final-deployment-guide.md # 部署指南
└── PROJECT_STRUCTURE.md          # 本文件
```

## 🎯 模块说明

### 系统管理模块 (module.system)
- **用户管理**: 用户CRUD、状态管理、角色分配
- **角色管理**: 角色CRUD、权限分配
- **部门管理**: 部门CRUD、层级管理
- **菜单管理**: 菜单CRUD、权限关联
- **权限管理**: 权限查看和管理
- **认证授权**: 登录、验证码、权限控制

### 公共模块 (common)
- **权限认证**: 基于SaToken的权限控制
- **数据缓存**: Redis缓存服务
- **异常处理**: 统一异常处理机制
- **结果封装**: 统一API返回格式
- **工具类**: 各种通用工具

## 🔐 权限体系

### 角色层级
1. **SUPER_ADMIN** - 超级管理员（所有权限）
2. **ADMIN** - 系统管理员（大部分权限）
3. **DEPT_ADMIN** - 部门管理员（部门相关权限）
4. **USER** - 普通用户（基本查看权限）
5. **OPERATOR** - 操作员（基本操作权限）

### 权限编码规范
```
system:模块:操作
例如：
- system:user:view     # 查看用户
- system:user:create   # 创建用户
- system:role:delete   # 删除角色
```

## 🚀 快速开始

### 1. 环境要求
- JDK 17+
- Maven 3.8+
- MySQL 8.0+
- Redis 6.0+

### 2. 数据库初始化
```bash
# 完整系统初始化（包含雪花算法ID生成）
mysql -u root -p cutover_control < src/main/resources/sql/sys_init.sql

# 可选：测试雪花算法ID生成函数
mysql -u root -p cutover_control < src/main/resources/sql/test_snowflake_id.sql
```

### 3. 启动应用
```bash
mvn spring-boot:run -Dspring-boot.run.profiles=dev
```

### 4. 访问系统
- API文档: http://localhost:10000/doc.html
- 健康检查: http://localhost:10000/actuator/health

### 5. 测试账户
| 用户名 | 密码 | 角色 |
|--------|------|------|
| superadmin | 123456 | 超级管理员 |
| admin | 123456 | 系统管理员 |

## 📋 开发规范

### 代码规范
- 使用Java 8+特性（Stream API、Optional等）
- 布尔类型变量使用xxxFlag命名
- 使用完整单词组合命名
- 接口名使用形容词（-able形式）
- 不允许魔法值，使用常量定义

### 分层规范
- Controller: 接收请求，参数校验，调用Service
- Service: 业务逻辑处理，事务控制
- DAO: 数据访问，使用MyBatis-Plus
- Model: 数据模型，包含Entity、DTO、Req、Resp

### 权限控制
- Controller层使用@SaCheckPermission注解
- Service层使用编程式权限检查
- 敏感操作增加额外权限验证

## 🔧 技术栈

### 核心框架
- **Spring Boot 3.x**: 主框架
- **Spring Security**: 安全框架（配合SaToken）
- **SaToken**: 权限认证框架

### 数据层
- **MyBatis-Plus**: ORM框架
- **MySQL**: 主数据库
- **Redis**: 缓存数据库
- **HikariCP**: 数据库连接池

### 工具库
- **Hutool**: Java工具库
- **MapStruct Plus**: 对象转换
- **Knife4j**: API文档
- **Lombok**: 代码简化

## 📈 后续规划

1. **业务模块开发**: 割接业务相关功能
2. **工作流集成**: Camunda BPM集成
3. **前端开发**: Vue3 + Element Plus
4. **监控告警**: 完善监控体系
5. **性能优化**: 根据使用情况优化

---

*项目基础架构已完成，可开始业务模块开发*
